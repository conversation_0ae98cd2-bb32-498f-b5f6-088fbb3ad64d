package com.hb.crm.core.beans.Notification;

import com.hb.crm.core.beans.Employee;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Document
@NoArgsConstructor
public class EmployeeNotificationSetting {

    @Id
    private String id;
    private boolean enableAll = true;
    private boolean enablePushNotification = true;
    private boolean enableInAppNotification = true;
    private boolean enableWhatsAppNotification = true;
    private boolean enableSmsAppNotification = true;
    private boolean enableEmailNotification = true;

    private List<EmployeeNotificationDisableSetting> disabledNotifications = new ArrayList<>();

    @DBRef(lazy = true)
    private Employee employee;

    public EmployeeNotificationSetting(Employee employee) {
        this.employee = employee;
    }
}
