package com.hb.crm.core.beans.Notification;

import com.hb.crm.core.Enums.DisableNotificationType;
import com.hb.crm.core.Enums.NotificationChannelType;
import com.hb.crm.core.Enums.NotificationType;
import com.hb.crm.core.beans.User;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Getter
@Setter
@Document
@NoArgsConstructor
public class NotificationSetting {

    @Id
    private String id;
    private boolean enableAll = true;
    private boolean enablePushNotification = true;
    private boolean enableInAppNotification = true;
    private boolean enableWhatsAppNotification = true;
    private boolean enableSmsAppNotification = true;
    private boolean enableEmailNotification = true;

    private List<NotificationDisableSetting> disabledNotifications = new ArrayList<>();

    @DBRef(lazy = true)
    private User user;

    public NotificationSetting(User user) {
        this.user = user;
    }
}
