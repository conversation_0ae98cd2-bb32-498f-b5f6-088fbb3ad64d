package com.hb.crm.core.beans;


import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.DBRef;

import java.time.LocalDateTime;

public class Activity {
    @Id
    private String id;
    private  String name;
    private  String details;
    private  LocalDateTime start;
    private  LocalDateTime end;
    @DBRef
    private  Place place;
    private  MediaWrapper media;
    @DBRef
    private ActivityCategory Category;

    private  Object OtherDetail;

    public Object getOtherDetail() {
        return OtherDetail;
    }

    public void setOtherDetail(Object otherDetail) {
        OtherDetail = otherDetail;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    public LocalDateTime getStart() {
        return start;
    }

    public void setStart(LocalDateTime start) {
        this.start = start;
    }

    public LocalDateTime getEnd() {
        return end;
    }

    public void setEnd(LocalDateTime end) {
        this.end = end;
    }

    public Place getPlace() {
        return place;
    }

    public void setPlace(Place place) {
        this.place = place;
    }

    public MediaWrapper getMedia() {
        return media;
    }

    public void setMedia(MediaWrapper media) {
        this.media = media;
    }

    public ActivityCategory getCategory() {
        return Category;
    }

    public void setCategory(ActivityCategory category) {
        Category = category;
    }
}
