package com.hb.crm.core.beans.Hotel;

import java.math.BigDecimal;
import java.util.ArrayList;

public class HotelAvilabiltyResponse {
    private  String propertyReferenceId;
    private  String name;
    private HotelAddress address;
    private ArrayList<Room> rooms;
    private ArrayList<Image> images;

    private BigDecimal pirceDeffrance;
    private BigDecimal standerRoomPrice;
    private  float latitude;
    private  float longitude;
    private  String city;
    private  String amenityDescriptions;
    private  String countryCode;
    private String checkinDate;
    private String checkoutDate;
    private  ArrayList<String> description;

    public String getPropertyReferenceId() {
        return propertyReferenceId;
    }

    public void setPropertyReferenceId(String propertyReferenceId) {
        this.propertyReferenceId = propertyReferenceId;
    }

    public BigDecimal getStanderRoomPrice() {
        return standerRoomPrice;
    }

    public void setStanderRoomPrice(BigDecimal standerRoomPrice) {
        this.standerRoomPrice = standerRoomPrice;
    }

    public BigDecimal getPirceDeffrance() {
        return pirceDeffrance;
    }

    public void setPirceDeffrance(BigDecimal pirceDeffrance) {
        this.pirceDeffrance = pirceDeffrance;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public HotelAddress getAddress() {
        return address;
    }

    public void setAddress(HotelAddress address) {
        this.address = address;
    }

    public ArrayList<Room> getRooms() {
        return rooms;
    }

    public void setRooms(ArrayList<Room> rooms) {
        this.rooms = rooms;
    }

    public ArrayList<Image> getImages() {
        return images;
    }

    public void setImages(ArrayList<Image> images) {
        this.images = images;
    }

    public float getLatitude() {
        return latitude;
    }

    public void setLatitude(float latitude) {
        this.latitude = latitude;
    }

    public float getLongitude() {
        return longitude;
    }

    public void setLongitude(float longitude) {
        this.longitude = longitude;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getAmenityDescriptions() {
        return amenityDescriptions;
    }

    public void setAmenityDescriptions(String amenityDescriptions) {
        this.amenityDescriptions = amenityDescriptions;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getCheckinDate() {
        return checkinDate;
    }

    public void setCheckinDate(String checkinDate) {
        this.checkinDate = checkinDate;
    }

    public String getCheckoutDate() {
        return checkoutDate;
    }

    public void setCheckoutDate(String checkoutDate) {
        this.checkoutDate = checkoutDate;
    }

    public ArrayList<String> getDescription() {
        return description;
    }

    public void setDescription(ArrayList<String> description) {
        this.description = description;
    }
}
