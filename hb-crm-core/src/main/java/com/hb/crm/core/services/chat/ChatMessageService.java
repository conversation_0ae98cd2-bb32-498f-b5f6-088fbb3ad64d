package com.hb.crm.core.services.chat;

import com.hb.crm.core.Enums.ConversationMessageType;
import com.hb.crm.core.Enums.chat.ChatMessageType;
import com.hb.crm.core.Enums.chat.DeletionType;
import com.hb.crm.core.beans.chat.GroupChat.GroupConversation;
import com.hb.crm.core.dtos.EmployeeDto;
import com.hb.crm.core.dtos.PageDto;
import com.hb.crm.core.dtos.UserDto;
import com.hb.crm.core.dtos.chat.request.*;
import com.hb.crm.core.dtos.chat.response.*;
import org.springframework.data.domain.Page;

import java.util.List;
import java.time.LocalDateTime;

public interface ChatMessageService {
    GroupConversation createGroupConversation(String subPackageId);

    UserConversationWithMassagesDto addUserToGroupConversation(String conversationId, String userId);

    void removeUserFromGroupConversation(String conversationId, String userId);

    Page<ChatMessageResponseDto> getConversationHistory(String conversationId, int page, int size, List<ChatMessageType> types);

    Page<GroupChatMessageResponseDto> getGroupConversationHistory(String conversationId, int page, int size, List<ConversationMessageType> types);

    void persistMessage(ChatMessageRequestDto message);

    void persistGroupMessage(GroupChatMessageRequestDto message);

    void retractVoteGroupChatMessage(VoteMessageRequestDto voteRequest);

    void voteChatMessage(VoteMessageRequestDto voteRequest);

    void voteGroupChatMessage(VoteMessageRequestDto voteRequest);

    PageDto<EmployeeDto> getGroupAdmins(String conversationId, boolean includeInactive, int page, int size);

    UserConversationWithMassagesDto addAdminToGroupConversation(String conversationId, String employeeId);

    void removeAdminFromGroupConversation(String conversationId, String employeeId);

    PageDto<UserDto> getGroupMembers(String conversationId, boolean includeInactive, int page, int size);

    void muteGroup(String userConversationId, boolean mute);

    void deleteMessage(List<String> messageId, String userId, DeletionType type);

    void deleteGroupMessage(List<String> messageId, String userId, DeletionType type);

    ConversationDto createConversation(String userId, String packageId);

    ConversationDto updateConversationSettings(String conversationId, Boolean isMuted, Boolean isClosed);

    void updateChatMessageStatus(UpdateChatMessageStatusRequest updateStatusRequest);

    void updateGroupChatMessageStatus(UpdateGroupChatMessageStatusRequest updateStatusRequest);

    long countUnreadGroupMessagesByConversation(String conversationId, String userId);
    long countAllUnreadGroupMessages(String userId);

    long countUnreadChatMessagesByConversation(String conversationId, String userId);
    long countAllUnreadChatMessages(String userId);

    List<ChatWithUnreadCountDto> getAllChatsWithUnreadCount(String userId);

    PageDto<MessageSearchResultDto> searchMessages(
        String searchText,
        LocalDateTime startDate,
        LocalDateTime endDate,
        int page,
        int size
    );

    PageDto<ChatWithUnreadCountDto> searchConversations(
            LocalDateTime startDate,
            LocalDateTime endDate,
            String readStatus,
            int page,
            int size,
            String sortDirection
    );

    ConversationDto consumeChatFromQueue(String agentId);

    void transferChatToAgent(String conversationId, String fromAgentId, String toAgentId);

    void assignChatToAgent(String conversationId, String agentId);

    void transferAgentQueueToMainQueue(String agentId);

    ConversationDto consumeChatFromAgentQueue(String agentId);

    public List<ConversationDto> getAllAgentQueueContent(String agentId);

    public List<ConversationDto> getMainQueue();

    void closeConversationForAgent(String conversationId, String agentId);

    void openConversationForAgent(String conversationId, String agentId);

    int getConversationQueuePosition(String conversationId);
}
