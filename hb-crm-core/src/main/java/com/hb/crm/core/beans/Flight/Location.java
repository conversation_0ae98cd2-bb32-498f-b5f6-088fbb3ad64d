package com.hb.crm.core.beans.Flight;

public class Location {
        private String code;
        private String value;
        private String city;
        private Object stateProvinceName;
        private String country;
        private String countryCode;
        private String iata;
        private Object icao;
        private float latitude;
        private float longitude;

        public String getCode() {
                return code;
        }

        public void setCode(String code) {
                this.code = code;
        }

        public String getValue() {
                return value;
        }

        public void setValue(String value) {
                this.value = value;
        }

        public String getCity() {
                return city;
        }

        public void setCity(String city) {
                this.city = city;
        }

        public Object getStateProvinceName() {
                return stateProvinceName;
        }

        public void setStateProvinceName(Object stateProvinceName) {
                this.stateProvinceName = stateProvinceName;
        }

        public String getCountry() {
                return country;
        }

        public void setCountry(String country) {
                this.country = country;
        }

        public String getCountryCode() {
                return countryCode;
        }

        public void setCountryCode(String countryCode) {
                this.countryCode = countryCode;
        }

        public String getIata() {
                return iata;
        }

        public void setIata(String iata) {
                this.iata = iata;
        }

        public Object getIcao() {
                return icao;
        }

        public void setIcao(Object icao) {
                this.icao = icao;
        }

        public float getLatitude() {
                return latitude;
        }

        public void setLatitude(float latitude) {
                this.latitude = latitude;
        }

        public float getLongitude() {
                return longitude;
        }

        public void setLongitude(float longitude) {
                this.longitude = longitude;
        }
}
