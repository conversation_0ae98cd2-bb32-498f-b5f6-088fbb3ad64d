package com.hb.crm.core.Enums;

import java.util.EnumSet;
import java.util.Set;

public enum PaymentProvider {
    TAMARA(EnumSet.of(PaymentCurrency.SAR, PaymentCurrency.AED , PaymentCurrency.KWD)),
    TABBY (EnumSet.of(PaymentCurrency.SAR, PaymentCurrency.AED,
            PaymentCurrency.KWD, PaymentCurrency.BHD,
            PaymentCurrency.QAR));

    private final Set<PaymentCurrency> supported;
    PaymentProvider(Set<PaymentCurrency> s) { this.supported = s; }
    public boolean supports(PaymentCurrency c) { return supported.contains(c); }
}