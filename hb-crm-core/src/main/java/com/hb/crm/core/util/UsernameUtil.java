package com.hb.crm.core.util;

import org.springframework.stereotype.Service;

import java.util.Random;

@Service
public class UsernameUtil {

    /**
     *
     * Generate username for user from first & last name,
     * Username is unique and can be used as primary key.
     *
     * @param firstName
     * @param lastName
     * @return String generated username
     */
    public static String generateUniqueUsername(String firstName, String lastName) {
        var s1 = formatString(firstName);
        var s2 = formatString(lastName);

        Random random = new Random();
        int number = random.nextInt(8000) + 1000;
        return String.format("%s%s%s", s1, s2, number);
    }

    /**
     *
     * format string to lowercase & remove whitespace
     *
     * @param text
     * @return String formated tex
     *
     */
    private static String formatString(String text) {
        var processedText = text.replaceAll("[^a-zA-Z0-9]", "").replaceAll(" ", "");
        return processedText.toLowerCase();
    }
}
