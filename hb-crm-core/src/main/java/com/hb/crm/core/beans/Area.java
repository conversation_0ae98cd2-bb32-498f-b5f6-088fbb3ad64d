package com.hb.crm.core.beans;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.DBRef;

import java.util.List;

@Setter
@Getter
public class Area {
    @Id
    private String id;
    private String name;
    private Double latitude; // Latitude for map location
    private Double longitude; // Longitude for map location
    private String countryId;
    private String country;
    // list of medias wrapper

    private List<MediaWrapper> medias;

    private String mapUrl;
    private String description;
    @DBRef
    private City city;

    public Area() {
    }
}
