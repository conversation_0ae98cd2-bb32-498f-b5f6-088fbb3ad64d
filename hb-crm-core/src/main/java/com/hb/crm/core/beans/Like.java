package com.hb.crm.core.beans;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.DBRef;

public class Like {
    @Id
    private  String id;
    private  String titel;
    private  String discription;
    @DBRef(lazy = true)
    private  SubPackage packageref;

    public SubPackage getPackageref() {
        return packageref;
    }

    public void setPackageref(SubPackage packageref) {
        this.packageref = packageref;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitel() {
        return titel;
    }

    public void setTitel(String titel) {
        this.titel = titel;
    }

    public String getDiscription() {
        return discription;
    }

    public void setDiscription(String discription) {
        this.discription = discription;
    }

    public Like(String titel, String discription, SubPackage packageref) {
        this.titel = titel;
        this.discription = discription;
        this.packageref = packageref;
    }
}
