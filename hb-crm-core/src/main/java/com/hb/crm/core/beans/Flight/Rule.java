package com.hb.crm.core.beans.Flight;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.springframework.data.mongodb.core.mapping.Field;
@JsonIgnoreProperties(ignoreUnknown = true)

public class Rule {
    private String category;
    private String maxPenaltyAmount;
    @Field("NotApplicable")
    private String notApplicable;

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getMaxPenaltyAmount() {
        return maxPenaltyAmount;
    }

    public void setMaxPenaltyAmount(String maxPenaltyAmount) {
        this.maxPenaltyAmount = maxPenaltyAmount;
    }

    public String getNotApplicable() {
        return notApplicable;
    }

    public void setNotApplicable(String notApplicable) {
        this.notApplicable = notApplicable;
    }
}
