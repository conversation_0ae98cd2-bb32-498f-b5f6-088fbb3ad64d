package com.hb.crm.core.beans.Notification;

import com.hb.crm.core.Enums.CrmNotificationChannelTypes;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Document
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CrmNotificationChannel {

    @Id
    String id;

    String name;
    boolean enabled;
    CrmNotificationChannelTypes channel;

}
