package com.hb.crm.core.beans;

import com.hb.crm.core.beans.FlightPackage.Airport;
import com.hb.crm.core.beans.PackagePlaces.PackageCountry;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@Document
@NoArgsConstructor
public class Package {
    @Id
    private String id;
    @Indexed()
    private String name;
    @DBRef(lazy = true)
    private User infulancer;
    @DBRef(lazy = true)
    private List<Tag> tags;
    @DBRef(lazy = true)
    private List<Mood> moods;
    private String description;
    private List<Rate> rates;
    private LocalDateTime start;
    private LocalDateTime end;
    private Airport fromAirport;
    private Airport toAirport;
    private MediaWrapper brochure;
    private List<PackageCountry> packagePlaces;
    private MediaWrapper mainImage;
    private int followMeDiscount;
    private boolean availableForFollowMe = false;
    private LocalDate availableFrom;
    private List<MediaWrapper> medias;
    private boolean fromAirportInside;
    private boolean toAirportInside;
    private String slug;
    private String followMeSlug;
    private int sharesCount;

    public Package(String id) {
        this.id = id;
    }

}
