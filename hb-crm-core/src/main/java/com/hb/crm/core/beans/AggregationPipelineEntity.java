package com.hb.crm.core.beans;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

@Document(collection = "aggregation_pipelines")
public class AggregationPipelineEntity {
    @Id
    private String id;
    private String name;
    private List<Document> pipeline;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<Document> getPipeline() {
        return pipeline;
    }

    public void setPipeline(List<Document> pipeline) {
        this.pipeline = pipeline;
    }
}
