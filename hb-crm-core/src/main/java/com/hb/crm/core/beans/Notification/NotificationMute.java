package com.hb.crm.core.beans.Notification;

import com.hb.crm.core.Enums.NotificationChannelType;
import com.hb.crm.core.Enums.NotificationEntityType;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Document
public class NotificationMute
{
    @Id
    private String id;
    private String userId;
    private String entityId;
    private NotificationEntityType entityType;
    private List<NotificationChannelType> channels = new ArrayList<>();
}
