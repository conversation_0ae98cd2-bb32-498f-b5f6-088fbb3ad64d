package com.hb.crm.core.repositories.LiveStream;

import java.util.List;

import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import com.hb.crm.core.beans.LiveStream.LiveStream;
import com.hb.crm.core.Enums.LiveStreamStatus;


public interface LiveStreamRepository extends MongoRepository<LiveStream, String> {
    
    @Query("{'infulancer.id': ?0, 'status': ?1}")
    LiveStream findByInfulancerIdAndStatus(String influencerId, LiveStreamStatus status);
    
    @Query("{'infulancer.id': ?0, 'status': ?1}")
    List<LiveStream> findAllByInfulancerIdAndStatus(String influencerId, LiveStreamStatus status);
    
    @Query("{'infulancer.id': ?0}")
    LiveStream findByInfulancerId(String influencerId);
}