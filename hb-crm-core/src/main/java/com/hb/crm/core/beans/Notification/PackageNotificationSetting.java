package com.hb.crm.core.beans.Notification;

import com.hb.crm.core.beans.SubPackage;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;

@Setter
@Getter
@Document
public class PackageNotificationSetting {

    @Id
    private String id;

    @DBRef(lazy = true)
    private SubPackage _package;

    private int packageCapacityFullPercentage;
}
