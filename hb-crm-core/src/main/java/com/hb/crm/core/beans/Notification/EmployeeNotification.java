package com.hb.crm.core.beans.Notification;

import com.hb.crm.core.Enums.*;
import com.hb.crm.core.beans.Employee;
import com.hb.crm.core.beans.User;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.Map;

@Getter
@Setter
@Document
public class EmployeeNotification {

    @Id
    private String id;
    private String subject;
    private String body;
    private boolean sent = false;
    private boolean read = false;
    private boolean hidden = false;
    private int retryCount = 0;
    private LocalDateTime readAt;
    private LocalDateTime sentAt;
    private LocalDateTime lastTriedAt;

    @CreatedDate
    private LocalDateTime createdAt;

    @DBRef(lazy = true)
    private Employee employee;

    private NotificationType type;
    private NotificationChannelType channelType;
    private String entityId;
    private String image;
    private Map<String, String> payload;
    private String icon;
    private NotificationEntityType entityType;
    private String entitySlug;
    private String username;
    private IconType iconType;
    private UserType navigatedUsertype;
}
