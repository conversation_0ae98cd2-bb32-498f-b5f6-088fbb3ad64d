package com.hb.crm.core.beans;

import org.springframework.data.annotation.Id;

import java.util.List;

public class Nav {
    @Id
    private  String id;
    private String title;
    private String version;
    private String key;
    private List<NavItems> navItems;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public List<NavItems> getNavItems() {
        return navItems;
    }

    public void setNavItems(List<NavItems> navItems) {
        this.navItems = navItems;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
}
