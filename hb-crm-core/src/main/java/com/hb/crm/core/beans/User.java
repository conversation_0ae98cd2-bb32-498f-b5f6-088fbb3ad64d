package com.hb.crm.core.beans;

import com.hb.crm.core.Enums.Gender;
import com.hb.crm.core.Enums.UserType;
import com.hb.crm.core.beans.Hotel.SocialMediaLink;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Setter
@Document
@NoArgsConstructor
public class User implements Serializable    {
    @Serial
    private static final long serialVersionUID = 711201638919389292L;
    @Getter
    protected String password;
    @Getter
    protected String code;
    @Getter
    private UserInfo userInfo;
    @Getter
    private LocalDateTime birthDate;
    @Getter
    @Id
    private String id;
    @Getter
    @CreatedDate
    private LocalDateTime creationDate;
    @Getter
    @LastModifiedDate
    private LocalDateTime updatedDate;
    @Getter
    @Indexed(unique = true)
    private String username;
    @Getter
    private String firebaseId;
    @Getter
    private List<String> fcmTokens = new ArrayList<>();
    @Getter
    private String about;
    @Getter
    private String firstName;
    @Getter
    private String lastName;
    @Getter
    private String guestEmail;
    @Getter
    private LocalDateTime accountLockDate;
    @Getter
    private Gender gender = Gender.male;
    @Getter
    private String role;

    @Getter
    private List<SocialMediaLink> socialMediaLinks;
    @Getter
    private boolean isActive = true;

    @Getter
    private boolean isLimited = false;

    @Getter
    private String collectionDeviceId;

    @Getter
    private int failedLoginAttempts = 0;

    @Getter
    private boolean accountLocked = false;

    @Getter
    private String reasonForLockedAccount;

    @Getter
    private String city;

    @Getter
    private String country;

    @Getter
    private String passResetKey;

    @Getter
    private String token;

    @Getter
    private int failedPasswordChanges = 0;

    @Getter
    @DBRef(lazy = true)
    private List<Mood> moods;


    @Getter
    private UserType usertype = UserType.Traveler;

    @Getter
    private boolean emailActivated = false;

    @Getter
    private String emailCode = "";
    @Getter
    private boolean phoneActiviated = false;

    @Getter
    private String phoneCode = "";
    @Getter
    private boolean lookoutEnabled = false;

    @Getter
    private int follwerscount = 0;

    @Getter
    private int followingcount = 0;

    @Getter
    private List<MediaWrapper> medias;

    @Getter
    private String coverImage;

    @Getter
    private String profileImage;

    @Getter
    private List<Provider> providers;

    @Getter
    @DBRef(lazy = true)
    private List<Post> postBookMark;
    @Getter
    private boolean privateProfile = false;
    private List<Like> like;

    @Getter
    @Setter
    private boolean isConnected = false;

    @Getter
    @Setter
    private LocalDateTime lastConnectionDate;

    @Getter
    @Setter
    private List<String> savedReelsIds = new ArrayList<>();

    @Getter
    @Setter
    private int sharesCount;

    @Getter
    @Setter
    private int numberOfSharingContent;


    public User(String id) {
        this.id = id;
    }

    public List<com.hb.crm.core.beans.Like> getLike() {
        if (this.like == null)
            return new ArrayList<com.hb.crm.core.beans.Like>();
        return this.like;
    }


}
