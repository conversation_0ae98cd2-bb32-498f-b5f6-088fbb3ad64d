package com.hb.crm.core.beans.Flight;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.math.BigDecimal;
import java.util.ArrayList;
@JsonIgnoreProperties(ignoreUnknown = true)
public class Price {
    private String currency;
    private BigDecimal total;
    private int markup;
    private double taxAndFees;
    private ArrayList<Fee> fees;
    private ArrayList<Object> additionalServices;
    private Object taxes;

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public BigDecimal getTotal() {
        return total;
    }

    public void setTotal(BigDecimal total) {
        this.total = total;
    }

    public int getMarkup() {
        return markup;
    }

    public void setMarkup(int markup) {
        this.markup = markup;
    }

    public double getTaxAndFees() {
        return taxAndFees;
    }

    public void setTaxAndFees(double taxAndFees) {
        this.taxAndFees = taxAndFees;
    }

    public ArrayList<Fee> getFees() {
        return fees;
    }

    public void setFees(ArrayList<Fee> fees) {
        this.fees = fees;
    }

    public ArrayList<Object> getAdditionalServices() {
        return additionalServices;
    }

    public void setAdditionalServices(ArrayList<Object> additionalServices) {
        this.additionalServices = additionalServices;
    }

    public Object getTaxes() {
        return taxes;
    }

    public void setTaxes(Object taxes) {
        this.taxes = taxes;
    }
}
