package com.hb.crm.core.services;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.RestClientException;
import org.apache.commons.codec.binary.Hex;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.hb.crm.core.services.interfaces.SpecTrumIntegrationService;
import com.hb.crm.core.exceptions.CustomException;
import com.hb.crm.core.dtos.spectTrum.HotelSearch;
import com.hb.crm.core.dtos.spectTrum.HotelResponse.HotelSearchResponseWrapper;
import com.hb.crm.core.dtos.spectTrum.FlightResponse.FlghtResponse;
import com.hb.crm.core.dtos.spectTrum.FlightResponse.FlghtResponseWaraper;
import com.hb.crm.core.dtos.spectTrum.FlightSearch.FlightSearch;
import com.hb.crm.core.dtos.spectTrum.HotelAvailabilityRequest;
import com.hb.crm.core.dtos.spectTrum.HotelAvailabilityResponse.HotelAvailabilityResponse;
import com.hb.crm.core.dtos.spectTrum.FlightConfirmPrice.FlightConfirmPriceRequest;
import com.hb.crm.core.dtos.spectTrum.FlightConfirmPrice.FlightConfirmPriceResponse;
import com.hb.crm.core.dtos.spectTrum.FlightBooking.FlightBookingRequest;
import com.hb.crm.core.dtos.spectTrum.FlightBooking.FlightBookingResponse;
import com.hb.crm.core.dtos.spectTrum.FlightBooking.GetBookingResponse;
import com.hb.crm.core.dtos.spectTrum.FlightBooking.CancelBookingResponse;
import com.hb.crm.core.dtos.spectTrum.HotelConfirmPrice.HotelConfirmPriceRequest;
import com.hb.crm.core.dtos.spectTrum.HotelConfirmPrice.HotelConfirmPriceResponse;
import com.hb.crm.core.dtos.spectTrum.HotelPreBook.HotelPreBookRequest;
import com.hb.crm.core.dtos.spectTrum.HotelPreBook.HotelPreBookResponse;
import com.hb.crm.core.dtos.spectTrum.HotelBooking.HotelBookingRequest;
import com.hb.crm.core.dtos.spectTrum.HotelBooking.HotelBookingResponse;
import com.hb.crm.core.dtos.spectTrum.HotelGetBooking.HotelGetBookingResponse;
import com.hb.crm.core.dtos.spectTrum.HotelCancelBooking.HotelCancelBookingResponse;
import org.apache.commons.codec.digest.HmacUtils;


@Service
public class SpecTrumIntegrationServiceImpl implements SpecTrumIntegrationService 
{
    @Value("${Spectrum.AuthUrl}")
    private String authBaseUrl;

    @Value("${Spectrum.FloghtBaseUrl}")
    private String flightBaseUrl;

    @Value("${Spectrum.HotelBaseUrl}")
    private String hotelBaseUrl;
    @Value("${Spectrum.api_key}")
    private String api_key;
    @Value("${Spectrum.agency_Id}")
    private  String agency_Id;
   
    
    @Override
    public String authorize() throws JsonProcessingException 
    {
        try
        {
          
            
        long unixTime = System.currentTimeMillis() / 1000L;
        String message = this.agency_Id + unixTime;
        // Step 1: Base64 encode the api_key (like Postman's btoa)
        String secretBase64 = Base64.getEncoder().encodeToString(this.api_key.getBytes(StandardCharsets.UTF_8));
        // Step 2: HMAC SHA-512 using base64-encoded secret
        String authKey = generateHmacSHA512(message, secretBase64);
        RestTemplate restTemplate = new RestTemplate();
        String url = this.authBaseUrl + "?key=" + authKey;
        HttpHeaders headers = new HttpHeaders();
            headers.add("x-agency-id", agency_Id);
        headers.add("Content-Type", "application/json; charset=utf-8");
        headers.add("x-request-time", String.valueOf(unixTime));
         MultiValueMap<String, String> queryParams = new LinkedMultiValueMap<>();
         HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(queryParams, headers);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, requestEntity, String.class);

            if (response.getStatusCode().is2xxSuccessful()) 
            {
                String responseBody = response.getBody();
                if (responseBody == null || responseBody.trim().isEmpty()) 
                {
                    throw new CustomException(500, "Authentication failed: Empty response from Spectrum API");
                }
                return responseBody;
            } 
            else 
            {
                throw new CustomException(response.getStatusCode().value(), 
                    "Authentication failed with HTTP status: " + response.getStatusCode().value());
            }
        } 
        catch (RestClientException e) 
        {
            throw new CustomException(500, "Failed to authenticate with Spectrum API: " + e.getMessage());
        } 
        catch (Exception e) 
        {
            if (e instanceof CustomException) 
            {
                throw e;
            }
            throw new CustomException(500, "Unexpected error during authentication: " + e.getMessage());
        }
    }

    /**
     * Searches for flights using the Spectrum API
     * 
     * This method makes a POST request to the Spectrum flight search endpoint
     * with the provided search criteria and returns the flight offers.
     * 
     * @param request Flight search request containing search criteria
     * @return FlightSearchResponse containing flight offers and metadata
     * @throws JsonProcessingException if JSON processing fails
     * @throws CustomException if search fails or API errors occur
     */
    @Override
    public FlghtResponse searchFlights(FlightSearch search) throws JsonProcessingException 
    {
        String token = this.authorize();
        String url = this.flightBaseUrl + String.format("/api/v1/beta/search?agencyId=%s&polling=false",agency_Id) ;
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json; charset=utf-8");
        headers.add("Authorization", "Bearer " + token);
        // Set the media type for JSON

        HttpEntity<Object> requestEntity = new HttpEntity<>(search, headers);

         RestTemplate restTemplate = new RestTemplate();
        var response = restTemplate.exchange
        (
                url,
                HttpMethod.POST,
                requestEntity,
                FlghtResponseWaraper.class
        );
        return  response.getBody().getData();
    }

    /**
     * Searches for hotels using the Spectrum API
     * 
     * This method makes a POST request to the Spectrum hotel search endpoint
     * with the provided search criteria and returns the hotel offers.
     * 
     * @param request Hotel search request containing search criteria
     * @return HotelSearchResponseWrapper containing hotel offers and metadata
     * @throws JsonProcessingException if JSON processing fails
     * @throws CustomException if search fails or API errors occur
     */
    @Override
    public HotelSearchResponseWrapper searchHotels(HotelSearch search) throws JsonProcessingException 
    {
        try 
        {
            // Get authorization token
            String token = this.authorize();
            
            // Build the hotel search URL
            String url = this.hotelBaseUrl + String.format("/api/v1/beta/search?agencyId=%s", agency_Id);
            
            // Set up headers
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", "application/json; charset=utf-8");
            headers.add("Authorization", "Bearer " + token);
            
            // Create the request entity
            HttpEntity<Object> requestEntity = new HttpEntity<>(search, headers);
            
            // Make the REST call
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<HotelSearchResponseWrapper> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                requestEntity,
                HotelSearchResponseWrapper.class
            );
            
            // Check response status
            if (response.getStatusCode().is2xxSuccessful()) 
            {
                HotelSearchResponseWrapper responseBody = response.getBody();
                if (responseBody == null) 
                {
                    throw new CustomException(500, "Hotel search failed: Empty response from Spectrum API");
                }
                return responseBody;
            } 
            else 
            {
                throw new CustomException(response.getStatusCode().value(), 
                    "Hotel search failed with HTTP status: " + response.getStatusCode().value());
            }
        } 
        catch (RestClientException e) 
        {
            throw new CustomException(500, "Failed to search hotels with Spectrum API: " + e.getMessage());
        } 
        catch (Exception e) 
        {
            if (e instanceof CustomException) 
            {
                throw e;
            }
            throw new CustomException(500, "Unexpected error during hotel search: " + e.getMessage());
        }
    }

    /**
     * Checks hotel availability using the Spectrum API
     * 
     * This method makes a POST request to the Spectrum hotel availability endpoint
     * with the provided search criteria and returns detailed availability information.
     * 
     * @param request Hotel availability request containing property details and occupancy
     * @return HotelAvailabilityResponse containing availability details, pricing, and room options
     * @throws JsonProcessingException if JSON processing fails
     * @throws CustomException if availability check fails or API errors occur
     */
    @Override
    public HotelAvailabilityResponse checkHotelAvailability(HotelAvailabilityRequest request) throws JsonProcessingException 
    {
        try 
        {
            // Get authorization token
            String token = this.authorize();
            
            // Build the hotel availability URL
            String url = this.hotelBaseUrl + String.format("/api/v1/beta/availability?agencyId=%s", agency_Id);
            
            // Set up headers
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", "application/json; charset=utf-8");
            headers.add("Authorization", "Bearer " + token);
            
            // Create the request entity
            HttpEntity<Object> requestEntity = new HttpEntity<>(request, headers);
            
            // Make the REST call
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<HotelAvailabilityResponse> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                requestEntity,
                HotelAvailabilityResponse.class
            );
            
            // Check response status
            if (response.getStatusCode().is2xxSuccessful()) 
            {
                HotelAvailabilityResponse responseBody = response.getBody();
                if (responseBody == null) 
                {
                    throw new CustomException(500, "Hotel availability check failed: Empty response from Spectrum API");
                }
                return responseBody;
            } 
            else 
            {
                throw new CustomException(response.getStatusCode().value(), 
                    "Hotel availability check failed with HTTP status: " + response.getStatusCode().value());
            }
        } 
        catch (RestClientException e) 
        {
            throw new CustomException(500, "Failed to check hotel availability with Spectrum API: " + e.getMessage());
        } 
        catch (Exception e) 
        {
            if (e instanceof CustomException) 
            {
                throw e;
            }
            throw new CustomException(500, "Unexpected error during hotel availability check: " + e.getMessage());
        }
    }

    /**
     * Confirms flight pricing before booking
     * 
     * This method validates and confirms flight pricing details with Spectrum API,
     * returning updated pricing information, booking requirements, and confirmation reference ID.
     * 
     * @param request Flight confirm price request containing flight offers and criteria
     * @return FlightConfirmPriceResponse containing confirmed pricing and requirements
     * @throws JsonProcessingException if JSON processing fails
     * @throws CustomException if confirm price fails or API errors occur
     */
    @Override
    public FlightConfirmPriceResponse confirmFlightPrice(FlightConfirmPriceRequest request) throws JsonProcessingException 
    {
        try 
        {
            // Get authentication token
            String token = this.authorize();
            
            // Build the confirm price URL
            String url = this.flightBaseUrl + String.format("/api/v1/beta/confirmprice?agencyId=%s", agency_Id);
            
            // Set up headers
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", "application/json; charset=utf-8");
            headers.add("Authorization", "Bearer " + token);
            
            // Create request entity
            HttpEntity<FlightConfirmPriceRequest> requestEntity = new HttpEntity<>(request, headers);
            
            // Make the API call
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<FlightConfirmPriceResponse> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                requestEntity,
                FlightConfirmPriceResponse.class
            );
            
            // Check response status and return data
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) 
            {
                return response.getBody();
            } 
            else 
            {
                throw new CustomException(response.getStatusCode().value(), 
                    "Flight confirm price failed with HTTP status: " + response.getStatusCode().value());
            }
        } 
        catch (RestClientException e) 
        {
            throw new CustomException(500, "Failed to confirm flight price with Spectrum API: " + e.getMessage());
        } 
        catch (Exception e) 
        {
            if (e instanceof CustomException) 
            {
                throw e;
            }
            throw new CustomException(500, "Unexpected error during flight price confirmation: " + e.getMessage());
        }
    }

    /**
     * Books a flight using the Spectrum API
     * 
     * This method creates a flight booking after price confirmation has been completed.
     * It requires a valid confirmPriceReferenceId and complete traveler information
     * including personal details, contact information, and travel documents.
     * 
     * @param request Flight booking request containing confirm price reference and traveler details
     * @return FlightBookingResponse containing booking confirmation and trip details
     * @throws JsonProcessingException if JSON processing fails
     * @throws CustomException if booking fails or API errors occur
     */
    @Override
    public FlightBookingResponse bookFlight(FlightBookingRequest request) throws JsonProcessingException 
    {
        try 
        {
            // Validate request
            if (request == null) 
            {
                throw new CustomException(400, "Flight booking request cannot be null");
            }
            
            if (request.getConfirmPriceReferenceId() == null || request.getConfirmPriceReferenceId().trim().isEmpty()) 
            {
                throw new CustomException(400, "Confirm price reference ID is required for flight booking");
            }
            
            if (request.getTravelers() == null || request.getTravelers().isEmpty()) 
            {
                throw new CustomException(400, "At least one traveler is required for flight booking");
            }
            
            // Get authentication token
            String token = this.authorize();
            
            // Build the flight booking URL
            String url = this.flightBaseUrl + String.format("/api/v1/beta/book?agencyId=%s", agency_Id);
            
            // Set up headers
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", "application/json; charset=utf-8");
            headers.add("Authorization", "Bearer " + token);
            
            // Create request entity
            HttpEntity<FlightBookingRequest> requestEntity = new HttpEntity<>(request, headers);
            
            // Make the API call
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<FlightBookingResponse> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                requestEntity,
                FlightBookingResponse.class
            );
            
            // Check response status and return data
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) 
            {
                FlightBookingResponse bookingResponse = response.getBody();
                
                // Check if the booking was successful according to the API response
                if (bookingResponse.getSuccess() != null && !bookingResponse.getSuccess()) 
                {
                    String errorMessage = "Flight booking failed";
                    if (bookingResponse.getMessage() != null && !bookingResponse.getMessage().trim().isEmpty()) 
                    {
                        errorMessage += ": " + bookingResponse.getMessage();
                    }
                    if (bookingResponse.getErrors() != null && !bookingResponse.getErrors().isEmpty()) 
                    {
                        errorMessage += " Errors: " + String.join(", ", bookingResponse.getErrors());
                    }
                    throw new CustomException(400, errorMessage);
                }
                
                return bookingResponse;
            } 
            else 
            {
                throw new CustomException(response.getStatusCode().value(), 
                    "Flight booking failed with HTTP status: " + response.getStatusCode().value());
            }
        } 
        catch (RestClientException e) 
        {
            throw new CustomException(500, "Failed to book flight with Spectrum API: " + e.getMessage());
        } 
        catch (Exception e) 
        {
            if (e instanceof CustomException) 
            {
                throw e;
            }
            throw new CustomException(500, "Unexpected error during flight booking: " + e.getMessage());
        }
    }

    /**
     * Retrieves booking details using the Spectrum API
     * 
     * This method gets detailed booking information for a created booking using the tripId.
     * Returns comprehensive booking details including flight information, passenger details,
     * fare rules, and booking status.
     * 
     * @param tripId The unique trip identifier for the booking
     * @return GetBookingResponse containing detailed booking information
     * @throws JsonProcessingException if JSON processing fails
     * @throws CustomException if retrieval fails or API errors occur
     */
    @Override
    public GetBookingResponse getBooking(String tripId) throws JsonProcessingException 
    {
        try 
        {
            // Validate tripId
            if (tripId == null || tripId.trim().isEmpty()) 
            {
                throw new CustomException(400, "Trip ID is required to retrieve booking details");
            }
            
            // Get authentication token
            String token = this.authorize();
            
            // Build the get booking URL
            String url = this.flightBaseUrl + String.format("/api/v1/beta/book/%s?agencyId=%s", 
                tripId.trim(), agency_Id);
            
            // Set up headers
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", "application/json; charset=utf-8");
            headers.add("Authorization", "Bearer " + token);
            
            // Create request entity (no body for GET request)
            HttpEntity<Void> requestEntity = new HttpEntity<>(headers);
            
            // Make the API call
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<GetBookingResponse> response = restTemplate.exchange(
                url,
                HttpMethod.GET,
                requestEntity,
                GetBookingResponse.class
            );
            
            // Check response status and return data
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) 
            {
                GetBookingResponse bookingResponse = response.getBody();
                
                // Check if the request was successful according to the API response
                if (bookingResponse.getSuccess() != null && !bookingResponse.getSuccess()) 
                {
                    String errorMessage = "Failed to retrieve booking details";
                    if (bookingResponse.getMessage() != null && !bookingResponse.getMessage().trim().isEmpty()) 
                    {
                        errorMessage += ": " + bookingResponse.getMessage();
                    }
                    if (bookingResponse.getErrors() != null && !bookingResponse.getErrors().isEmpty()) 
                    {
                        errorMessage += " Errors: " + String.join(", ", bookingResponse.getErrors());
                    }
                    throw new CustomException(400, errorMessage);
                }
                
                return bookingResponse;
            } 
            else 
            {
                throw new CustomException(response.getStatusCode().value(), 
                    "Get booking failed with HTTP status: " + response.getStatusCode().value());
            }
        } 
        catch (RestClientException e) 
        {
            throw new CustomException(500, "Failed to retrieve booking from Spectrum API: " + e.getMessage());
        } 
        catch (Exception e) 
        {
            if (e instanceof CustomException) 
            {
                throw e;
            }
            throw new CustomException(500, "Unexpected error during booking retrieval: " + e.getMessage());
        }
    }

    /**
     * Cancels a flight booking using the Spectrum API
     * 
     * This method cancels an existing flight booking using the tripId and bookingReferenceId.
     * Returns cancellation status and details including any applicable penalties or refund information.
     * 
     * @param tripId The unique trip identifier for the booking
     * @param bookingReferenceId The booking reference identifier
     * @return CancelBookingResponse containing cancellation status and details
     * @throws JsonProcessingException if JSON processing fails
     * @throws CustomException if cancellation fails or API errors occur
     */
    @Override
    public CancelBookingResponse cancelBooking(String tripId, String bookingReferenceId) throws JsonProcessingException 
    {
        try 
        {
            // Validate parameters
            if (tripId == null || tripId.trim().isEmpty()) 
            {
                throw new CustomException(400, "Trip ID is required to cancel booking");
            }
            
            if (bookingReferenceId == null || bookingReferenceId.trim().isEmpty()) 
            {
                throw new CustomException(400, "Booking reference ID is required to cancel booking");
            }
            
            // Get authentication token
            String token = this.authorize();
            
            // Build the cancel booking URL
            String url = this.flightBaseUrl + String.format("/api/v1/beta/cancelbooking/%s/%s?agencyId=%s", 
                tripId.trim(), bookingReferenceId.trim(), agency_Id);
            
            // Set up headers
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", "application/json; charset=utf-8");
            headers.add("Authorization", "Bearer " + token);
            
            // Create request entity (no body for DELETE request)
            HttpEntity<Void> requestEntity = new HttpEntity<>(headers);
            
            // Make the API call
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<CancelBookingResponse> response = restTemplate.exchange(
                url,
                HttpMethod.DELETE,
                requestEntity,
                CancelBookingResponse.class
            );
            
            // Check response status and return data
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) 
            {
                CancelBookingResponse cancelResponse = response.getBody();
                
                // Check if the request was successful according to the API response
                if (cancelResponse.getSuccess() != null && !cancelResponse.getSuccess()) 
                {
                    String errorMessage = "Failed to cancel booking";
                    if (cancelResponse.getMessage() != null && !cancelResponse.getMessage().trim().isEmpty()) 
                    {
                        errorMessage += ": " + cancelResponse.getMessage();
                    }
                    if (cancelResponse.getErrors() != null && !cancelResponse.getErrors().isEmpty()) 
                    {
                        errorMessage += " Errors: " + String.join(", ", cancelResponse.getErrors());
                    }
                    throw new CustomException(400, errorMessage);
                }
                
                // Additional check for cancellation status in the data
                if (cancelResponse.getData() != null && 
                    cancelResponse.getData().getStatus() != null && 
                    !cancelResponse.getData().getStatus()) 
                {
                    String errorMessage = "Booking cancellation failed";
                    if (cancelResponse.getData().getMessage() != null && !cancelResponse.getData().getMessage().trim().isEmpty()) 
                    {
                        errorMessage += ": " + cancelResponse.getData().getMessage();
                    }
                    if (cancelResponse.getData().getErrorCode() != null && !cancelResponse.getData().getErrorCode().trim().isEmpty()) 
                    {
                        errorMessage += " (Error Code: " + cancelResponse.getData().getErrorCode() + ")";
                    }
                    throw new CustomException(400, errorMessage);
                }
                
                return cancelResponse;
            } 
            else 
            {
                throw new CustomException(response.getStatusCode().value(), 
                    "Cancel booking failed with HTTP status: " + response.getStatusCode().value());
            }
        } 
        catch (RestClientException e) 
        {
            throw new CustomException(500, "Failed to cancel booking with Spectrum API: " + e.getMessage());
        } 
        catch (Exception e) 
        {
            if (e instanceof CustomException) 
            {
                throw e;
            }
            throw new CustomException(500, "Unexpected error during booking cancellation: " + e.getMessage());
        }
    }

    /**
     * Confirms hotel pricing using the Spectrum API
     * 
     * This method validates and confirms hotel pricing details for selected property and room options.
     * Returns detailed property information with confirmed pricing, room options, and policies.
     * 
     * @param request Hotel confirm price request containing property reference and room selections
     * @return HotelConfirmPriceResponse containing confirmed pricing and property details
     * @throws JsonProcessingException if JSON processing fails
     * @throws CustomException if confirmation fails or API errors occur
     */
    @Override
    public HotelConfirmPriceResponse confirmHotelPrice(HotelConfirmPriceRequest request) throws JsonProcessingException 
    {
        try 
        {
            // Validate request
            if (request == null) 
            {
                throw new CustomException(400, "Hotel confirm price request cannot be null");
            }
            
            if (request.getPropertyReferenceId() == null || request.getPropertyReferenceId().trim().isEmpty()) 
            {
                throw new CustomException(400, "Property reference ID is required for hotel price confirmation");
            }
            
            if (request.getRooms() == null || request.getRooms().isEmpty()) 
            {
                throw new CustomException(400, "At least one room selection is required for hotel price confirmation");
            }
            
            // Get authentication token
            String token = this.authorize();
            
            // Build the hotel confirm price URL
            String url = this.hotelBaseUrl + String.format("/api/v1/beta/confirmprice?agencyId=%s", agency_Id);
            
            // Set up headers
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", "application/json; charset=utf-8");
            headers.add("Authorization", "Bearer " + token);
            
            // Create request entity
            HttpEntity<HotelConfirmPriceRequest> requestEntity = new HttpEntity<>(request, headers);
            
            // Make the API call
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<HotelConfirmPriceResponse> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                requestEntity,
                HotelConfirmPriceResponse.class
            );
            
            // Check response status and return data
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) 
            {
                HotelConfirmPriceResponse confirmResponse = response.getBody();
                
                // Check if the request was successful according to the API response
                if (confirmResponse.getSuccess() != null && !confirmResponse.getSuccess()) 
                {
                    String errorMessage = "Hotel price confirmation failed";
                    if (confirmResponse.getMessage() != null && !confirmResponse.getMessage().trim().isEmpty()) 
                    {
                        errorMessage += ": " + confirmResponse.getMessage();
                    }
                    if (confirmResponse.getErrors() != null && !confirmResponse.getErrors().isEmpty()) 
                    {
                        errorMessage += " Errors: " + String.join(", ", confirmResponse.getErrors());
                    }
                    throw new CustomException(400, errorMessage);
                }
                
                return confirmResponse;
            } 
            else 
            {
                throw new CustomException(response.getStatusCode().value(), 
                    "Hotel confirm price failed with HTTP status: " + response.getStatusCode().value());
            }
        } 
        catch (RestClientException e) 
        {
            throw new CustomException(500, "Failed to confirm hotel price with Spectrum API: " + e.getMessage());
        } 
        catch (Exception e) 
        {
            if (e instanceof CustomException) 
            {
                throw e;
            }
            throw new CustomException(500, "Unexpected error during hotel price confirmation: " + e.getMessage());
        }
    }

    /**
     * Creates a hotel pre-booking summary using the Spectrum API
     * 
     * This method checks booking summary before creating the actual hotel booking confirmation.
     * Returns booking reference ID, trip ID, and pre-booking status for final confirmation.
     * 
     * @param request Hotel pre-book request containing guest details and room selections
     * @return HotelPreBookResponse containing booking reference and pre-booking status
     * @throws JsonProcessingException if JSON processing fails
     * @throws CustomException if pre-booking fails or API errors occur
     */
    @Override
    public HotelPreBookResponse hotelPreBookSummary(HotelPreBookRequest request) throws JsonProcessingException 
    {
        try 
        {
            // Validate request
            if (request == null) 
            {
                throw new CustomException(400, "Hotel pre-book request cannot be null");
            }
            
            if (request.getPropertyReferenceId() == null || request.getPropertyReferenceId().trim().isEmpty()) 
            {
                throw new CustomException(400, "Property reference ID is required for hotel pre-booking");
            }
            
            if (request.getEmail() == null || request.getEmail().trim().isEmpty()) 
            {
                throw new CustomException(400, "Email is required for hotel pre-booking");
            }
            
            if (request.getPhone() == null || 
                request.getPhone().getCountryCode() == null || request.getPhone().getCountryCode().trim().isEmpty() ||
                request.getPhone().getNumber() == null || request.getPhone().getNumber().trim().isEmpty()) 
            {
                throw new CustomException(400, "Valid phone number with country code is required for hotel pre-booking");
            }
            
            if (request.getRooms() == null || request.getRooms().isEmpty()) 
            {
                throw new CustomException(400, "At least one room is required for hotel pre-booking");
            }
            
            // Validate that each room has at least one guest
            for (int i = 0; i < request.getRooms().size(); i++) 
            {
                if (request.getRooms().get(i).getGuests() == null || request.getRooms().get(i).getGuests().isEmpty()) 
                {
                    throw new CustomException(400, "Room " + (i + 1) + " must have at least one guest");
                }
            }
            
            // Get authentication token
            String token = this.authorize();
            
            // Build the hotel pre-book URL
            String url = this.hotelBaseUrl + String.format("/api/v1/beta/prebook/%s", agency_Id);
            
            // Set up headers
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", "application/json; charset=utf-8");
            headers.add("Authorization", "Bearer " + token);
            
            // Create request entity
            HttpEntity<HotelPreBookRequest> requestEntity = new HttpEntity<>(request, headers);
            
            // Make the API call
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<HotelPreBookResponse> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                requestEntity,
                HotelPreBookResponse.class
            );
            
            // Check response status and return data
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) 
            {
                HotelPreBookResponse preBookResponse = response.getBody();
                
                // Check if the request was successful according to the API response
                if (preBookResponse.getSuccess() != null && !preBookResponse.getSuccess()) 
                {
                    String errorMessage = "Hotel pre-booking failed";
                    if (preBookResponse.getMessage() != null && !preBookResponse.getMessage().trim().isEmpty()) 
                    {
                        errorMessage += ": " + preBookResponse.getMessage();
                    }
                    if (preBookResponse.getErrors() != null && !preBookResponse.getErrors().isEmpty()) 
                    {
                        errorMessage += " Errors: " + String.join(", ", preBookResponse.getErrors());
                    }
                    throw new CustomException(400, errorMessage);
                }
                
                return preBookResponse;
            } 
            else 
            {
                throw new CustomException(response.getStatusCode().value(), 
                    "Hotel pre-book failed with HTTP status: " + response.getStatusCode().value());
            }
        } 
        catch (RestClientException e) 
        {
            throw new CustomException(500, "Failed to create hotel pre-booking with Spectrum API: " + e.getMessage());
        } 
        catch (Exception e) 
        {
            if (e instanceof CustomException) 
            {
                throw e;
            }
            throw new CustomException(500, "Unexpected error during hotel pre-booking: " + e.getMessage());
        }
    }

    /**
     * Creates a confirmed hotel booking using the Spectrum API
     * 
     * This method finalizes the hotel booking after pre-booking summary has been completed.
     * Uses the trip ID and booking reference ID from the pre-booking to confirm the final booking.
     * 
     * @param request Hotel booking request containing trip ID and booking reference ID
     * @return HotelBookingResponse containing final booking confirmation and status
     * @throws JsonProcessingException if JSON processing fails
     * @throws CustomException if booking fails or API errors occur
     */
    @Override
    public HotelBookingResponse hotelCreateBooking(HotelBookingRequest request) throws JsonProcessingException 
    {
        try 
        {
            // Validate request
            if (request == null) 
            {
                throw new CustomException(400, "Hotel booking request cannot be null");
            }
            
            if (request.getTripId() == null || request.getTripId().trim().isEmpty()) 
            {
                throw new CustomException(400, "Trip ID is required for hotel booking confirmation");
            }
            
            if (request.getBookingReferenceId() == null || request.getBookingReferenceId().trim().isEmpty()) 
            {
                throw new CustomException(400, "Booking reference ID is required for hotel booking confirmation");
            }
            
            // Set agencyId if not provided
            if (request.getAgencyId() == null) 
            {
                try 
                {
                    request.setAgencyId(Integer.parseInt(agency_Id));
                } 
                catch (NumberFormatException e) 
                {
                    throw new CustomException(400, "Invalid agency ID format");
                }
            }
            
            // Get authentication token
            String token = this.authorize();
            
            // Build the hotel booking URL
            String url = this.hotelBaseUrl + "/api/v1/beta/booking";
            
            // Set up headers
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", "application/json; charset=utf-8");
            headers.add("Authorization", "Bearer " + token);
            
            // Create request entity
            HttpEntity<HotelBookingRequest> requestEntity = new HttpEntity<>(request, headers);
            
            // Make the API call
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<HotelBookingResponse> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                requestEntity,
                HotelBookingResponse.class
            );
            
            // Check response status and return data
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) 
            {
                HotelBookingResponse bookingResponse = response.getBody();
                
                // Check if the request was successful according to the API response
                if (bookingResponse.getSuccess() != null && !bookingResponse.getSuccess()) 
                {
                    String errorMessage = "Hotel booking confirmation failed";
                    if (bookingResponse.getMessage() != null && !bookingResponse.getMessage().trim().isEmpty()) 
                    {
                        errorMessage += ": " + bookingResponse.getMessage();
                    }
                    if (bookingResponse.getErrors() != null && !bookingResponse.getErrors().isEmpty()) 
                    {
                        errorMessage += " Errors: " + String.join(", ", bookingResponse.getErrors());
                    }
                    throw new CustomException(400, errorMessage);
                }
                
                return bookingResponse;
            } 
            else 
            {
                throw new CustomException(response.getStatusCode().value(), 
                    "Hotel booking confirmation failed with HTTP status: " + response.getStatusCode().value());
            }
        } 
        catch (RestClientException e) 
        {
            throw new CustomException(500, "Failed to create hotel booking with Spectrum API: " + e.getMessage());
        } 
        catch (Exception e) 
        {
            if (e instanceof CustomException) 
            {
                throw e;
            }
            throw new CustomException(500, "Unexpected error during hotel booking confirmation: " + e.getMessage());
        }
    }

    /**
     * Retrieves detailed hotel booking information using the Spectrum API
     * 
     * This method gets comprehensive hotel booking details including property information, room details,
     * guest information, pricing, policies, and flight details (for package bookings).
     * 
     * @param tripId The unique trip identifier for the hotel booking
     * @return HotelGetBookingResponse containing detailed hotel booking information
     * @throws JsonProcessingException if JSON processing fails
     * @throws CustomException if retrieval fails or API errors occur
     */
    @Override
    public HotelGetBookingResponse getHotelBooking(String tripId) throws JsonProcessingException 
    {
        try 
        {
            // Validate tripId
            if (tripId == null || tripId.trim().isEmpty()) 
            {
                throw new CustomException(400, "Trip ID is required to retrieve hotel booking details");
            }
            
            // Get authentication token
            String token = this.authorize();
            
            // Build the get hotel booking URL
            String url = this.hotelBaseUrl + String.format("/api/v1/beta/booking/%s", tripId.trim());
            
            // Set up headers
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", "application/json; charset=utf-8");
            headers.add("Authorization", "Bearer " + token);
            
            // Create request entity (no body for GET request)
            HttpEntity<Void> requestEntity = new HttpEntity<>(headers);
            
            // Make the API call
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<HotelGetBookingResponse> response = restTemplate.exchange(
                url,
                HttpMethod.GET,
                requestEntity,
                HotelGetBookingResponse.class
            );
            
            // Check response status and return data
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) 
            {
                HotelGetBookingResponse hotelBookingResponse = response.getBody();
                
                // Check if the request was successful according to the API response
                if (hotelBookingResponse.getSuccess() != null && !hotelBookingResponse.getSuccess()) 
                {
                    String errorMessage = "Failed to retrieve hotel booking details";
                    if (hotelBookingResponse.getMessage() != null && !hotelBookingResponse.getMessage().trim().isEmpty()) 
                    {
                        errorMessage += ": " + hotelBookingResponse.getMessage();
                    }
                    if (hotelBookingResponse.getErrors() != null && !hotelBookingResponse.getErrors().isEmpty()) 
                    {
                        errorMessage += " Errors: " + String.join(", ", hotelBookingResponse.getErrors());
                    }
                    throw new CustomException(400, errorMessage);
                }
                
                return hotelBookingResponse;
            } 
            else 
            {
                throw new CustomException(response.getStatusCode().value(), 
                    "Get hotel booking failed with HTTP status: " + response.getStatusCode().value());
            }
        } 
        catch (RestClientException e) 
        {
            throw new CustomException(500, "Failed to retrieve hotel booking from Spectrum API: " + e.getMessage());
        } 
        catch (Exception e) 
        {
            if (e instanceof CustomException) 
            {
                throw e;
            }
            throw new CustomException(500, "Unexpected error during hotel booking retrieval: " + e.getMessage());
        }
    }

    /**
     * Cancels a hotel booking using the Spectrum API
     * 
     * This method cancels a confirmed hotel booking with support for partial cancellations
     * by specifying room IDs, and handling pre-cancellation references.
     * 
     * @param tripId The unique trip identifier for the hotel booking (required)
     * @param bookingReferenceId The booking reference ID (optional)
     * @param roomIds Comma-separated list of room IDs to cancel (optional - if not provided, cancels entire booking)
     * @param preCancellationReferences Pre-cancellation reference IDs (optional)
     * @return HotelCancelBookingResponse indicating cancellation success or failure
     * @throws JsonProcessingException if JSON processing fails
     * @throws CustomException if cancellation fails or API errors occur
     */
    @Override
    public HotelCancelBookingResponse cancelHotelBooking(String tripId, String bookingReferenceId, 
                                                         String roomIds, String preCancellationReferences) throws JsonProcessingException 
    {
        try 
        {
            // Validate tripId
            if (tripId == null || tripId.trim().isEmpty()) 
            {
                throw new CustomException(400, "Trip ID is required to cancel hotel booking");
            }
            
            // Get authentication token
            String token = this.authorize();
            
            // Build the cancel hotel booking URL with query parameters
            StringBuilder urlBuilder = new StringBuilder();
            urlBuilder.append(this.hotelBaseUrl)
                     .append(String.format("/api/v1/beta/cancel/%s", tripId.trim()));
            
            // Add optional query parameters
            boolean hasQueryParams = false;
            
            if (bookingReferenceId != null && !bookingReferenceId.trim().isEmpty()) 
            {
                urlBuilder.append(hasQueryParams ? "&" : "?");
                urlBuilder.append("bookingReferenceId=").append(bookingReferenceId.trim());
                hasQueryParams = true;
            }
            
            if (roomIds != null && !roomIds.trim().isEmpty()) 
            {
                urlBuilder.append(hasQueryParams ? "&" : "?");
                urlBuilder.append("roomIds=").append(roomIds.trim());
                hasQueryParams = true;
            }
            
            if (preCancellationReferences != null && !preCancellationReferences.trim().isEmpty()) 
            {
                urlBuilder.append(hasQueryParams ? "&" : "?");
                urlBuilder.append("preCancellationReferences=").append(preCancellationReferences.trim());
                hasQueryParams = true;
            }
            
            String url = urlBuilder.toString();
            
            // Set up headers
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", "application/json; charset=utf-8");
            headers.add("Authorization", "Bearer " + token);
            
            // Create request entity (no body for DELETE request)
            HttpEntity<Void> requestEntity = new HttpEntity<>(headers);
            
            // Make the API call
            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<HotelCancelBookingResponse> response = restTemplate.exchange(
                url,
                HttpMethod.DELETE,
                requestEntity,
                HotelCancelBookingResponse.class
            );
            
            // Check response status and return data
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) 
            {
                HotelCancelBookingResponse cancelResponse = response.getBody();
                
                // Check if the request was successful according to the API response
                if (cancelResponse.getSuccess() != null && !cancelResponse.getSuccess()) 
                {
                    String errorMessage = "Failed to cancel hotel booking";
                    if (cancelResponse.getMessage() != null && !cancelResponse.getMessage().trim().isEmpty()) 
                    {
                        errorMessage += ": " + cancelResponse.getMessage();
                    }
                    if (cancelResponse.getErrors() != null && !cancelResponse.getErrors().isEmpty()) 
                    {
                        errorMessage += " Errors: " + String.join(", ", cancelResponse.getErrors());
                    }
                    throw new CustomException(400, errorMessage);
                }
                
                // Additional validation - check if cancellation was actually successful
                if (cancelResponse.getData() != null && !cancelResponse.getData()) 
                {
                    String errorMessage = "Hotel booking cancellation failed";
                    if (cancelResponse.getMessage() != null && !cancelResponse.getMessage().trim().isEmpty()) 
                    {
                        errorMessage += ": " + cancelResponse.getMessage();
                    }
                    throw new CustomException(400, errorMessage);
                }
                
                return cancelResponse;
            } 
            else 
            {
                throw new CustomException(response.getStatusCode().value(), 
                    "Cancel hotel booking failed with HTTP status: " + response.getStatusCode().value());
            }
        } 
        catch (RestClientException e) 
        {
            throw new CustomException(500, "Failed to cancel hotel booking via Spectrum API: " + e.getMessage());
        } 
        catch (Exception e) 
        {
            if (e instanceof CustomException) 
            {
                throw e;
            }
            throw new CustomException(500, "Unexpected error during hotel booking cancellation: " + e.getMessage());
        }
    }
   
    
    ///////////////////////
    /// Helper Methods/////
    ///////////////////////
    
    private String generateHmacSHA512(String message, String secretKey) 
    {
        try 
        {
            byte[] hmacSha512 = new HmacUtils("HmacSHA512", secretKey.getBytes()).hmac(message.getBytes());
            return Hex.encodeHexString(hmacSha512);
        } 
        catch (Exception e) 
        {
            throw new CustomException(500, "Failed to generate HMAC SHA-512 signature: " + e.getMessage());
        }
    }
}
