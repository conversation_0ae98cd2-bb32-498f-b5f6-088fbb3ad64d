package com.hb.crm.core.beans;

import com.hb.crm.core.Enums.DataFixStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataFix {
    @Id
    private String id;
    private String name;

    public DataFix(String name, String id, String description, String methodName, String url, LocalDateTime date, DataFixStatus status) {
        this.name = name;
        this.id = id;
        this.description = description;
        this.methodName = methodName;
        this.url = url;
        this.date = date;
        this.status = status;
    }

    private String description;
    private String methodName;
    private String url;
    private LocalDateTime date;
    private DataFixStatus status;
    private long matchedCount;   // <-- count of docs matched by the query
    private long modifiedCount;  // <-- count of docs actually modified


}
