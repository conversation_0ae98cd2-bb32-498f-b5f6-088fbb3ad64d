package com.hb.crm.core.config;
import org.bson.Document;
import org.springframework.core.convert.converter.Converter;
public class SearchWriteConverter implements Converter<Object, Document> {
    @Override
    public Document convert(Object source) {
        // Implement your generic write logic here
        if (source instanceof Document) {
            return (Document) source;
        }
        // Transform other types to Document as needed
        Document doc = new Document();
        // Populate the document with fields from the source object
        return doc;
    }
}