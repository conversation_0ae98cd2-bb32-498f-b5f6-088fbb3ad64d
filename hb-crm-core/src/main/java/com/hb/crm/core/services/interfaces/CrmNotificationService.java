package com.hb.crm.core.services.interfaces;

import com.hb.crm.core.Enums.*;
import com.hb.crm.core.beans.Employee;
import com.hb.crm.core.beans.Notification.CrmNotification;
import com.hb.crm.core.beans.Notification.EmployeeNotification;
import com.hb.crm.core.beans.Notification.Notification;
import com.hb.crm.core.beans.User;
import com.hb.crm.core.dtos.PageDto;
import com.hb.crm.core.dtos.clientNotification.GroupedNotificationsDto;
import com.hb.crm.core.dtos.clientNotification.NotificationDto;
import com.hb.crm.core.dtos.notification.CreateCrmNotificationDto;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;


@Service
public interface CrmNotificationService {



    boolean sendWhatsAppMessage(String to, String message);

    void sendAndStoreNotification(String entityId, NotificationType type,
                                  Employee employee, List<Object> entities,
                                  String image, String icon,
                                  NotificationEntityType entityType,
                                  User navigatedUser,
                                  String slug,
                                  String username,
                                  UserType navigatedUserType);

    /**
     * @param token
     * @param title
     * @param body
     * @param payload
     * @apiNote Send notification to a single user by fcm token with data payload
     * @return: boolean that determine if the message sent successfully or not
     */
    boolean sendNotification(String token, String title, String body, Map<String, String> payload);

    boolean sendNotification(String token, NotificationType type, String employeeId);

    /**
     * @param topic
     * @param title
     * @param body
     * @apiNote Send broadcast notification to topic
     */
    public void sendBroadcastNotification(String topic, String title, String body);


    /**
     * @param topic
     * @param title
     * @param body
     * @param payload
     * @apiNote Send broadcast notification to a topic with payload data
     */
    public void sendBroadcastNotification(
            String topic,
            String title,
            String body,
            Map<String, String> payload
    );

    EmployeeNotification storeNotification(String subject, String body, NotificationType type, NotificationChannelType channelType,
                                          Employee employee, boolean sent, String entityId, String image, Map<String, String> payload,
                                          String icon,
                                          NotificationEntityType entityType,
                                          User navigatedUser,
                                          String slug,
                                          String username,
                                          IconType iconType,
                                          UserType navigatedUserType);

    com.hb.crm.core.dtos.PageDto<NotificationDto> getNotifications(String employeeId, int page, int size);

    com.hb.crm.core.dtos.PageDto<NotificationDto> getUnreadNotifications(String employeeId, int page, int size);

    Long getUnreadNotificationsCount(String employeeId);

    /**
     * Updates the read status of a notification
     *
     * @param id   the notification ID
     * @param read true to mark as read, false to mark as unread
     */
    void updateNotificationReadStatus(String id, boolean read);

    Page<EmployeeNotification> search();

    PageDto<EmployeeNotification> search(Map<String, Object> obj, int page, int i);

    EmployeeNotification getNotificationById(String id);

    void delete(String id);

    void update(EmployeeNotification obj);

    boolean sendSmsNotification(String phoneNumber, String message);

    void sendEmail(String email, String subject, String body);

    GroupedNotificationsDto getGroupedNotifications(String employeeId, int page, int size);

    GroupedNotificationsDto getGroupedUnreadNotifications(String employeeId, int page, int size);

    PageDto<EmployeeNotification> getAllNotificationsWithFilter(int page, int size, Boolean sent);

    boolean retrySingleNotification(String notificationId);

    void hideNotification(String notificationId, String employeeId);

    void hideNotifications(List<String> notificationIds, String employeeId);

}
