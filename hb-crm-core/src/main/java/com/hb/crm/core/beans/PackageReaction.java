package com.hb.crm.core.beans;

import com.hb.crm.core.CombinedKeys.UserPackageKey;
import org.springframework.data.annotation.Id;

public class PackageReaction {
    @Id
    private UserPackageKey id;
    private boolean loveIt;

    public UserPackageKey getId() {
        return id;
    }

    public void setId(UserPackageKey id) {
        this.id = id;
    }

    public boolean isLoveIt() {
        return loveIt;
    }

    public void setLoveIt(boolean loveIt) {
        this.loveIt = loveIt;
    }
}
