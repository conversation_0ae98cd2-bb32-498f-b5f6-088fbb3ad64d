package com.hb.crm.core.beans;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.DBRef;

import java.time.LocalDateTime;
import java.util.List;

@Setter
@Getter
public class Comment {
    @Id
    private String id;
    private String comment;
    @DBRef(lazy = true)
    private Post post;
    @DBRef(lazy = true)
    private User user;

    @DBRef(lazy = true)
    private List<User> mentions;

    @DBRef(lazy = true)
    private Media media;

    private LocalDateTime createdDate;
    private int numberOfReactions;
    private int numberOfReplyes;

}
