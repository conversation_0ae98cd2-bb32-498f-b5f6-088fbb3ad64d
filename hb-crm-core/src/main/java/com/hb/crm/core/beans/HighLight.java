package com.hb.crm.core.beans;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.DBRef;

import java.util.List;
@Data
public class HighLight {
    @Id
    private String id;
    @DBRef(lazy = true)
    private User influencer;
    private String name;
    @DBRef(lazy = true)
    private List<Post> stories; // List of posts (stories)
    private String image; // URL or path to the image

}
