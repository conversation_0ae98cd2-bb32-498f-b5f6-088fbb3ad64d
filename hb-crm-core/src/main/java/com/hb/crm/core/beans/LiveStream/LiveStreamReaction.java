package com.hb.crm.core.beans.LiveStream;

import com.hb.crm.core.beans.Reaction;
import com.hb.crm.core.beans.User;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;

@Getter
@Setter
@NoArgsConstructor
@Document(collection = "liveStreamReaction")
public class LiveStreamReaction extends Reaction {
    
    @DBRef
    private LiveStream liveStream;
    
    public LiveStreamReaction(User user, LiveStream liveStream) {
        this.setUser(user);
        this.liveStream = liveStream;
    }
}