package com.hb.crm.core.beans;

import com.hb.crm.core.CombinedKeys.SubscribeKey;
import com.hb.crm.core.Enums.SubscribeStatus;
import com.hb.crm.core.beans.Flight.FlgihtCabin;
import com.hb.crm.core.beans.FlightPackage.Airport;
import com.hb.crm.core.beans.Hotel.BookingHotelResponse;
import com.hb.crm.core.beans.Hotel.RoomReservation;
import com.hb.crm.core.beans.Travelers.Traveler;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.DBRef;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
public class Subscribe {
    @Id
    private SubscribeKey id;
    private BigDecimal total;
    private SubscribeStatus status;
    private ArrayList<Traveler> travelers;
    private List<RoomReservation> RoomReservations;
    private List<BookingHotelResponse> hotels;
    private List<BookingFlightResponse> flights;
    private List<String> confirmPriceRefraceId;
    private List<FlgihtCabin> flgihtCabins;
    private List<String> documents;
    private String bockingDetailsUrl;
    private int numberOfRooms;
    private int adultCount;
    private int childCount;
    private Airport departureFlight;
    private Airport destinationFlight;
    private List<Preferences> preferences;
    private String otherHotelPreferences;
    private String otherFlightPreferences;
    private LocalDateTime expireDate;
    private  boolean fromAirportInside;
    private  boolean toAirportInside;
    @LastModifiedDate
    private LocalDateTime updateDate;

    @DBRef
    private List<PaymentTransaction> paymentTransaction  ;
    private String receiptPdfUrl;

}
