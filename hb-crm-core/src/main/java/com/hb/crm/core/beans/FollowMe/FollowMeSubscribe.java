package com.hb.crm.core.beans.FollowMe;

import com.hb.crm.core.CombinedKeys.SubscribeKey;
import com.hb.crm.core.Enums.FollowMeSubscribeStatus;
import com.hb.crm.core.beans.BookingFlightResponse;
import com.hb.crm.core.beans.Flight.Flight;
import com.hb.crm.core.beans.Flight.FlgihtCabin;
import com.hb.crm.core.beans.FlightPackage.Airport;
import com.hb.crm.core.beans.Hotel.BookingHotelResponse;
import com.hb.crm.core.beans.Hotel.Hotel;
import com.hb.crm.core.beans.Hotel.RoomReservation;
import com.hb.crm.core.beans.PackagePlaces.PackageCountry;
import com.hb.crm.core.beans.PaymentTransaction;

import com.hb.crm.core.beans.Travelers.Traveler;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
@Document(collection = "followMeSubscription")
public class FollowMeSubscribe 
{
    @Id
    private SubscribeKey id;
    private BigDecimal total;
    private FollowMeSubscribeStatus status;
    private ArrayList<Traveler> travelers;
    private int adultCount;
    private int childCount;
    private List<PackageCountry> packagePlaces = new ArrayList<PackageCountry>();
    
    // Search and selection data (before booking)
    private List<Hotel> hotels=new ArrayList<Hotel>();
    private List<Flight> flights=new ArrayList<Flight>();
    private List<RoomReservation> RoomReservations=new ArrayList<RoomReservation>();
    
    // Booking results (following Subscribe.java pattern)
    private List<BookingHotelResponse> bookedHotels=new ArrayList<BookingHotelResponse>();
    private List<BookingFlightResponse> bookedFlights=new ArrayList<BookingFlightResponse>();

    // ==================== PRICE CONFIRMATION RESULTS ====================
    
    // Flight pricing details from Spectrum API
    private List<String> flightConfirmPriceReferenceIds;
    private BigDecimal totalFlightPrice;
    private BigDecimal flightServiceFees;
    private BigDecimal flightTaxes;
    
    // Hotel pricing details from Spectrum API
    private BigDecimal totalHotelPrice;
    private BigDecimal hotelServiceFees;
    private BigDecimal hotelTaxes;
    
    // Total pricing breakdown
    private BigDecimal subtotalPrice; // flights + hotels
    private BigDecimal totalServiceFees; // flight + hotel service fees
    private BigDecimal totalTaxes; // flight + hotel taxes
    private BigDecimal finalTotalPrice; // subtotal + fees + taxes
    private String bockingDetailsUrl;
    
    // Price confirmation status and tracking
    private boolean pricesConfirmed;
    private String earliestExpiryDate;
    private LocalDateTime priceConfirmationDate;
    
    // ==================== SUBSCRIPTION MANAGEMENT FIELDS ====================
    
    // Room and booking management
    private int numberOfRooms;
    
    // Flight preferences and airport information
    private List<FlgihtCabin> flightCabins;
    private Airport departureFlight;
    private Airport destinationFlight;
    private String otherFlightPreferences;
    
    // Subscription expiration
    private LocalDateTime expireDate;
    
    // Booking status tracking
    private boolean bookingCompleted;
    private LocalDateTime bookingCompletionDate;

    @DBRef
    private List<PaymentTransaction> paymentTransaction  ;
    private String receiptPdfUrl;
    // Audit fields
    @LastModifiedDate
    private LocalDateTime updateDate;

}
