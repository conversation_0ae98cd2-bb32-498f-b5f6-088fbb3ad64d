package com.hb.crm.core.config;

import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.Indexes;
import jakarta.annotation.PostConstruct;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.core.MongoTemplate;


@Configuration
public class MongoSearchIndexConfig {
    private final MongoTemplate mongoTemplate2;

    public MongoSearchIndexConfig(@Qualifier("mongoTemplate2") MongoTemplate mongoTemplate2) {
        this.mongoTemplate2 = mongoTemplate2;
    }

    @PostConstruct
    public void init() {
        MongoDatabase database = mongoTemplate2.getDb();
        MongoCollection<Document> collection = database.getCollection("ReactionSearch");
        // Create the index
        collection.createIndex(Indexes.compoundIndex(Indexes.ascending("userId"), Indexes.ascending("update")));
        collection.createIndex(Indexes.compoundIndex(Indexes.ascending("userId"), Indexes.ascending("entityId")));

        System.out.println("Index created");
    }
}
