package com.hb.crm.core.beans.PackagePlaces;

import lombok.Data;
import java.util.List;

@Data
public class PackageCity {

    private String propertyId;
    private String cityName;
    private Integer order; // Order of the city within the country
    private List<PackageArea> areas; // List of areas in the city
    private List<PackageHotel> hotels; // Hotels linked to the city
    private List<PackageTransportation> transportation;
    private List<PackageItinerary> itinerary;
     // Transportation linked to the city

}
