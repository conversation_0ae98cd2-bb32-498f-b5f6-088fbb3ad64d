package com.hb.crm.core.services.interfaces;

import com.hb.crm.core.Enums.ReportStatus;
import com.hb.crm.core.dtos.CreateReportReplyDto;
import com.hb.crm.core.dtos.CreateUpdateReportDto;
import com.hb.crm.core.dtos.PageDto;
import com.hb.crm.core.dtos.ReportDto;
import org.springframework.stereotype.Service;

@Service
public interface ReportService {
    ReportDto create(CreateUpdateReportDto createDto , String userId);
    ReportDto update(String id, CreateUpdateReportDto updateDto , String userId);
    void delete(String id);
    ReportDto findById(String id);
    PageDto<ReportDto> search(String entityId, String entityName,String userID, int page, int limit);
    PageDto<ReportDto> findByUserId(String userId, int page, int limit);
    ReportDto addReply(String reportId, CreateReportReplyDto reply, String userId );
    ReportDto updateStatus(String reportId, ReportStatus status);
}