package com.hb.crm.core.services.interfaces;

import com.hb.crm.core.beans.Notification.CrmNotificationChannel;
import com.hb.crm.core.dtos.PageDto;
import com.hb.crm.core.dtos.notification.CreateCrmNotificationChannelDto;
import com.hb.crm.core.dtos.notification.UpdateCrmNotificationChannelDto;
import org.springframework.stereotype.Service;

@Service
public interface CrmNotificationChannelService {

    public CrmNotificationChannel create(CreateCrmNotificationChannelDto dto);

    public CrmNotificationChannel update(UpdateCrmNotificationChannelDto dto);
    public  String subscribeChannel(String id,String userId);
    public PageDto<CrmNotificationChannel> search( int page, int limit);

    public void delete(String id);


}
