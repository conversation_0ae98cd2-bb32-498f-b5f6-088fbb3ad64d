package com.hb.crm.core.beans.Notification;

import com.hb.crm.core.Enums.NotificationChannelType;
import com.hb.crm.core.Enums.NotificationType;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

@Getter
@Setter
@Document
public class NotificationTemplate {

    @Id
    private String id;
    private List<NotificationChannelType> channelTypes;
    private NotificationType notificationType;
    private boolean status = false;
    private NotificationTemplatePush push;
    private NotificationTemplateInApp inApp;
    private NotificationTemplateEmail email;
    private NotificationTemplateSms sms;
    private NotificationTemplateWhatsApp whatsApp;
}
