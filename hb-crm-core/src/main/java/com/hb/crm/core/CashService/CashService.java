package com.hb.crm.core.CashService;


import com.hb.crm.core.beans.FlightAndHotels;

import java.util.List;

public interface CashService {
    public void storeData(String key, Object value,Long lifetimeMillis );
    public Object retrieveData(String key);
    public UserClaimResult retrieveUserData(String key);
    public void storeData(String key, UserClaimResult value,Long lifetimeMillis );
    public  void  deleteData(String key);
    void deleteFlightAndHotels(String id);
    List<FlightAndHotels> getAllFlightAndHotels();
    FlightAndHotels getFlightAndHotelsById(String id);
    void storeFlightAndHotels(FlightAndHotels flightAndHotels, Long lifetimeMillis);
    void storeUserFCMToken(String userId, String fcmToken);
    String getUserFcmToken(String userId);
    void deleteUserFCMToken(String userId);

    // Employee FCM token methods
    void storeEmployeeFCMToken(String employeeId, String fcmToken);
    String getEmployeeFcmToken(String employeeId);
    void deleteEmployeeFCMToken(String employeeId);
 }
