package com.hb.crm.core.beans.PackagePlaces;

import com.hb.crm.core.beans.FlightPackage.Airport;
import lombok.Data;

@Data

public class PackageTransportation {
    private String propertyId;
    private String type; // Transportation type (e.g., Flight, Bus, Train, etc.)
    private String from; // Departure location
    private String to; // Arrival location
    private Airport fromAirport; // Departure Airport location
    private Airport toAirport; // Arrival Airport location
    private String description; // Additional details about the transportation
    private String imageUrl; // Image URL for transportation
    private Double price; // Price of the transportation
    private String startDate; // Start date of the transportation
    private String endDate; // End date of the transportation
    private Double latitudeFrom; // Latitude for departure location
    private Double longitudeFrom; // Longitude for departure location
    private Double latitudeTo; // Latitude for arrival location
    private Double longitudeTo; // Longitude for arrival location
    private String companyName;
    private String tripNumber;
    private Boolean inside;

}
