package com.hb.crm.core.beans;

import com.hb.crm.core.beans.Flight.FlightsDirections;
import com.hb.crm.core.beans.Hotel.HotelAvilabiltyResponse;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;

import java.time.LocalDateTime;
import java.util.ArrayList;

@Data
public class FlightAndHotels {
    @Id
    private String id;
    private Boolean used = false;
    private LocalDateTime creationTime;
    private ArrayList<HotelAvilabiltyResponse> Hotels;
    private ArrayList<FlightsDirections> Flights;
    private String packageId;
}
