package com.hb.crm.core.beans.Notification;

import com.hb.crm.core.beans.User;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.HashMap;

@Getter
@Setter
@Document
@NoArgsConstructor
@AllArgsConstructor
public class CrmNotification {

    @Id
    String id;

    String title;

    String body;
    String topic;
    String mobileNumber;
    HashMap<String, String> payload;

    boolean read;

    LocalDateTime sentAt;
    LocalDateTime readDate;

    @DBRef
    CrmNotificationChannel channel;

    @DBRef
    User user;



}
