package com.hb.crm.core.config;

import com.hb.crm.core.services.LiveStreamViewerService;
import com.hb.crm.core.services.LiveStreamSessionManager;
import com.hb.crm.core.dtos.LiveStream.ViewerCountUpdateDto;
import lombok.RequiredArgsConstructor;
import org.springframework.context.event.EventListener;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.messaging.SessionConnectEvent;
import org.springframework.web.socket.messaging.SessionDisconnectEvent;

@Component
@RequiredArgsConstructor
public class WebSocketEventListener {

    private final LiveStreamViewerService viewerService;
    private final LiveStreamSessionManager sessionManager;
    private final SimpMessagingTemplate messagingTemplate;

    @EventListener
    public void handleWebSocketConnectListener(SessionConnectEvent event) {
        SimpMessageHeaderAccessor headerAccessor = SimpMessageHeaderAccessor.wrap(event.getMessage());
        String sessionId = headerAccessor.getSessionId();
        
        System.out.println("🔌 WebSocket connection established - Session ID: " + sessionId);
    }

    @EventListener
    public void handleWebSocketDisconnectListener(SessionDisconnectEvent event) {
        SimpMessageHeaderAccessor headerAccessor = SimpMessageHeaderAccessor.wrap(event.getMessage());
        String liveStreamId = (String) headerAccessor.getSessionAttributes().get("liveStreamId");
        String userId = (String) headerAccessor.getSessionAttributes().get("userId");
        String username = (String) headerAccessor.getSessionAttributes().get("username");
        String sessionId = headerAccessor.getSessionId();
        
        System.out.println("🔌 WebSocket disconnection detected - Session ID: " + sessionId + ", User: " + username + " (" + userId + "), Stream: " + liveStreamId);
        
        if (liveStreamId != null && userId != null && sessionId != null) {
            try {
                // Get session info before removing
                LiveStreamSessionManager.SessionInfo sessionInfo = sessionManager.getSessionInfo(sessionId);
                
                if (sessionInfo != null) {
                    System.out.println("📊 Processing disconnect for " + (sessionInfo.isStreamer() ? "STREAMER" : "VIEWER") + 
                                      " - User: " + sessionInfo.getUsername() + " (" + sessionInfo.getUserId() + ")");
                    
                    // Remove session from session manager
                    sessionManager.removeSession(sessionId);
                    
                    // Update viewer count only if this was a viewer, not a streamer
                    if (!sessionInfo.isStreamer()) {
                        ViewerCountUpdateDto update = viewerService.removeViewer(liveStreamId, userId);
                        messagingTemplate.convertAndSend("/topic/live-stream/" + liveStreamId + "/viewers", update);
                        System.out.println("📉 Viewer count updated for stream: " + liveStreamId + " - New count: " + update.getViewersCount());
                    } else {
                        System.out.println("👑 Streamer disconnected from stream: " + liveStreamId);
                    }
                } else {
                    System.out.println("⚠️ Session info not found for session: " + sessionId + " - Attempting cleanup anyway");
                    // Still attempt to remove the session even if info is not found
                    sessionManager.removeSession(sessionId);
                }
                
                System.out.println("✅ WebSocket disconnection processed successfully");
                
            } catch (Exception e) {
                System.err.println("❌ Error handling WebSocket disconnect for session " + sessionId + ": " + e.getMessage());
                e.printStackTrace();
                
                // Try to cleanup session even if there was an error
                try {
                    sessionManager.removeSession(sessionId);
                    System.out.println("🧹 Emergency cleanup completed for session: " + sessionId);
                } catch (Exception cleanupError) {
                    System.err.println("❌ Emergency cleanup failed for session " + sessionId + ": " + cleanupError.getMessage());
                }
            }
        } else {
            System.out.println("ℹ️ WebSocket disconnection without live stream context - Session ID: " + sessionId);
        }
    }
}