package com.hb.crm.core.beans.Flight;

import java.util.ArrayList;

public class Itinerary {
    private int segmentGroupId;
    private String flightFrom;
    private String flightTo;
    private String duration;
    private ArrayList<Segment> segments;
    private Move departureAirport;
    private Move arrivalAirport;
    private ArrayList<Object> layoverAirports;
    private String departure;
    private String arrival;
    private int stops;
    private ArrayList<String> marketingCarrierCode;
    private ArrayList<String> operatingCarrierCode;
    private Time durationTime;
    private ArrayList<Object> layoverTime;
    private Time totalLayoverTime;

    public int getSegmentGroupId() {
        return segmentGroupId;
    }

    public void setSegmentGroupId(int segmentGroupId) {
        this.segmentGroupId = segmentGroupId;
    }

    public String getFlightFrom() {
        return flightFrom;
    }

    public void setFlightFrom(String flightFrom) {
        this.flightFrom = flightFrom;
    }

    public String getFlightTo() {
        return flightTo;
    }

    public void setFlightTo(String flightTo) {
        this.flightTo = flightTo;
    }

    public String getDuration() {
        return duration;
    }

    public void setDuration(String duration) {
        this.duration = duration;
    }

    public ArrayList<Segment> getSegments() {
        return segments;
    }

    public void setSegments(ArrayList<Segment> segments) {
        this.segments = segments;
    }

    public Move getDepartureAirport() {
        return departureAirport;
    }

    public void setDepartureAirport(Move departureAirport) {
        this.departureAirport = departureAirport;
    }

    public Move getArrivalAirport() {
        return arrivalAirport;
    }

    public void setArrivalAirport(Move arrivalAirport) {
        this.arrivalAirport = arrivalAirport;
    }

    public ArrayList<Object> getLayoverAirports() {
        return layoverAirports;
    }

    public void setLayoverAirports(ArrayList<Object> layoverAirports) {
        this.layoverAirports = layoverAirports;
    }



    public int getStops() {
        return stops;
    }

    public void setStops(int stops) {
        this.stops = stops;
    }

    public ArrayList<String> getMarketingCarrierCode() {
        return marketingCarrierCode;
    }

    public void setMarketingCarrierCode(ArrayList<String> marketingCarrierCode) {
        this.marketingCarrierCode = marketingCarrierCode;
    }

    public ArrayList<String> getOperatingCarrierCode() {
        return operatingCarrierCode;
    }

    public void setOperatingCarrierCode(ArrayList<String> operatingCarrierCode) {
        this.operatingCarrierCode = operatingCarrierCode;
    }

    public Time getDurationTime() {
        return durationTime;
    }

    public void setDurationTime(Time durationTime) {
        this.durationTime = durationTime;
    }

    public ArrayList<Object> getLayoverTime() {
        return layoverTime;
    }

    public void setLayoverTime(ArrayList<Object> layoverTime) {
        this.layoverTime = layoverTime;
    }

    public Time getTotalLayoverTime() {
        return totalLayoverTime;
    }

    public void setTotalLayoverTime(Time totalLayoverTime) {
        this.totalLayoverTime = totalLayoverTime;
    }

    public String getDeparture() {
        return departure;
    }

    public void setDeparture(String departure) {
        this.departure = departure;
    }

    public String getArrival() {
        return arrival;
    }

    public void setArrival(String arrival) {
        this.arrival = arrival;
    }
}
