package com.hb.crm.core.beans;

import com.hb.crm.core.CombinedKeys.UserPackageKey;
import org.springframework.data.annotation.Id;

public class PackageNotification {
    @Id
    private UserPackageKey id;
    private boolean state;

    public UserPackageKey getId() {
        return id;
    }

    public void setId(UserPackageKey id) {
        this.id = id;
    }

    public boolean isState() {
        return state;
    }

    public void setState(boolean state) {
        this.state = state;
    }

}

