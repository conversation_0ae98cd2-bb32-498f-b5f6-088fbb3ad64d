package com.hb.crm.core.beans;

import com.hb.crm.core.Enums.Gender;
import com.hb.crm.core.Enums.chat.AgentConversationStatus;
import com.hb.crm.core.beans.chat.QueuedConversation;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Setter
@Getter
@Document
public class Employee {

    @Id
    private String id;
    protected String password;
    protected String code;
    private LocalDateTime creationDate;
    private LocalDateTime updatedDate;

    @Indexed(unique = true)
    private String username;
    private String fullName;
    private String mobile;
    private String profilePicture;
    private String email;

    @DBRef
    private List<Role> roles;

    private int failedLoginAttempts = 0;
    private boolean accountLocked = false;
    private String reasonForLockedAccount;
    private String passResetKey;
    private int failedPasswordChanges = 0;
    private Gender gender;
    private LocalDateTime lastConnectionDate;
    private AgentConversationStatus agentConversationStatus = AgentConversationStatus.OFFLINE;
    private List<QueuedConversation> agentQueue = new ArrayList<>();

    // FCM tokens for push notifications
    private List<String> fcmTokens = new ArrayList<>();
    private String firebaseId;

}
