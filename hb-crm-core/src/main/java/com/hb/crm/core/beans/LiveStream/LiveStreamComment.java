package com.hb.crm.core.beans.LiveStream;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;

import com.hb.crm.core.beans.User;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "liveStreamComment")
public class LiveStreamComment {
    @Id
    private String id;
    private String comment;
    @DBRef
    private LiveStream liveStream;
    @DBRef
    private User user;
    private LocalDateTime createdDate;
    private LocalDateTime updatedDate;
    
    // Constructor for creating new comments
    public LiveStreamComment(String comment, LiveStream liveStream, User user) {
        this.comment = comment;
        this.liveStream = liveStream;
        this.user = user;
        this.createdDate = LocalDateTime.now();
        this.updatedDate = LocalDateTime.now();
    }
} 