package com.hb.crm.core.beans;

import com.hb.crm.core.Enums.PlaceType;
import org.springframework.data.annotation.Id;

public class Place {
    @Id
    private  String id;
    private  String name;
    private  String details;
    private  MediaWrapper media;

    private PlaceType placeType;
    private  String code;
    private  Category category;

    public Category getCategory() {
        return  category;
    }
    public void setCategory(Category category) {
        this.category = category;
    }
    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }
    public String getDetails() {
        return details;
    }
    public void setDetails(String details) {
        this.details = details;
    }
    public MediaWrapper getMedia() {
        return media;
    }
    public void setMedia(MediaWrapper media) {
        this.media = media;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public PlaceType getPlaceType() {
        return placeType;
    }

    public void setPlaceType(PlaceType placeType) {
        this.placeType = placeType;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
