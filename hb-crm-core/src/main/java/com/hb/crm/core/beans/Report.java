package com.hb.crm.core.beans;

import com.hb.crm.core.Enums.ReportEntity;
import com.hb.crm.core.Enums.ReportStatus;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

import java.time.LocalDateTime;
import java.util.ArrayList;


@Setter
@Getter
@Document
public class Report {

    @Id
    private String id;
    private String reason;
    @CreatedDate
    private LocalDateTime created;
    @LastModifiedDate
    private LocalDateTime modified;
    private String userId;
    private List<String> entityId;
    private ReportEntity entityName;
    private ReportStatus status = ReportStatus.PENDING;
    private List<ReportReply> replies = new ArrayList<>();
}
