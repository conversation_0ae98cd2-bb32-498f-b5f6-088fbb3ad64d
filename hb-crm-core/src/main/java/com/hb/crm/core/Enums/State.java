package com.hb.crm.core.Enums;

public enum State {
    NotStarted(0),
    Canceled(1),
    Completed(2),
    OnGoing(3);

    private final int value;
    State(int value) {
        this.value = value;
    }
    public int getValue() {
        return value;
    }
    public static State fromValue(int value) {
        for (State state : values()) {
            if (state.getValue() == value) {
                return state;
            }
        }
        throw new IllegalArgumentException("Unknown enum value: " + value);
    }
}
