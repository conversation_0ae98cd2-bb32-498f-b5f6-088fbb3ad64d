package com.hb.crm.core.beans.Hotel;

import java.math.BigDecimal;

public class TotalPrice {
    private String currency;
    private BigDecimal grossPrice;
    private BigDecimal taxAndFee;
    private BigDecimal commission;
    private DueAtProperty dueAtProperty;

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public BigDecimal getGrossPrice() {
        return grossPrice;
    }

    public void setGrossPrice(BigDecimal grossPrice) {
        this.grossPrice = grossPrice;
    }

    public BigDecimal getTaxAndFee() {
        return taxAndFee;
    }

    public void setTaxAndFee(BigDecimal taxAndFee) {
        this.taxAndFee = taxAndFee;
    }

    public BigDecimal getCommission() {
        return commission;
    }

    public void setCommission(BigDecimal commission) {
        this.commission = commission;
    }

    public DueAtProperty getDueAtProperty() {
        return dueAtProperty;
    }

    public void setDueAtProperty(DueAtProperty dueAtProperty) {
        this.dueAtProperty = dueAtProperty;
    }
}
