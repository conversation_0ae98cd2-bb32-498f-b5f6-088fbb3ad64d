package com.hb.crm.core.beans;

import com.hb.crm.core.Enums.PackageStatus;
import com.hb.crm.core.Enums.PostType;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.DBRef;

import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
public class Post {
    @Id
    private String id;
    private String text;
    private List<MediaWrapper> media;
    private float latitude;
    private float longtuid;
    private String place;
    private String mapUrl;


    @Indexed
    private String slug;

    @DBRef(lazy = true)
    private User user;
    private PostType postType;

    @DBRef(lazy = true)
    private Package Package;
    private LocalDateTime created;
    private LocalDateTime update;
    private LocalDateTime PostedDate;



    @DBRef(lazy = true)
    private List<Tag> tags;
    private PackageStatus postStatus = PackageStatus.draft;
    private String rejectionNote;
    private String note;
    private String description;

    private int commentsCount;
    private int reactsCount;
    private int viewsCount;
    private int sharesCount;

    private List<StoryOverlay> overlays;

    @DBRef(lazy = true)
    private List<User> taggedUsers;

    public Post(String id) {
        this.id = id;
    }

}
