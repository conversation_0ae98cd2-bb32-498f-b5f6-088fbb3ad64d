package com.hb.crm.core.beans.Flight;

import java.util.ArrayList;

public class Segment {
    private String duration;
    private Time segmentDurationInTime;
    private Move departure;
    private Move arrival;
    private String carrierCode;
    private String number;
    private Aircraft aircraft;
    private Operating operating;
    private String id;
    private int numberOfStops;
    private boolean blacklistedInEU;
    private ArrayList<Object> co2Emissions;

    public String getDuration() {
        return duration;
    }

    public void setDuration(String duration) {
        this.duration = duration;
    }

    public Time getSegmentDurationInTime() {
        return segmentDurationInTime;
    }

    public void setSegmentDurationInTime(Time segmentDurationInTime) {
        this.segmentDurationInTime = segmentDurationInTime;
    }

    public Move getDeparture() {
        return departure;
    }

    public void setDeparture(Move departure) {
        this.departure = departure;
    }

    public Move getArrival() {
        return arrival;
    }

    public void setArrival(Move arrival) {
        this.arrival = arrival;
    }

    public String getCarrierCode() {
        return carrierCode;
    }

    public void setCarrierCode(String carrierCode) {
        this.carrierCode = carrierCode;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public Aircraft getAircraft() {
        return aircraft;
    }

    public void setAircraft(Aircraft aircraft) {
        this.aircraft = aircraft;
    }

    public Operating getOperating() {
        return operating;
    }

    public void setOperating(Operating operating) {
        this.operating = operating;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public int getNumberOfStops() {
        return numberOfStops;
    }

    public void setNumberOfStops(int numberOfStops) {
        this.numberOfStops = numberOfStops;
    }

    public boolean isBlacklistedInEU() {
        return blacklistedInEU;
    }

    public void setBlacklistedInEU(boolean blacklistedInEU) {
        this.blacklistedInEU = blacklistedInEU;
    }

    public ArrayList<Object> getCo2Emissions() {
        return co2Emissions;
    }

    public void setCo2Emissions(ArrayList<Object> co2Emissions) {
        this.co2Emissions = co2Emissions;
    }
}
