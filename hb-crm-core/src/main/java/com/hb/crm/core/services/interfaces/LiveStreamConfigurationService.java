package com.hb.crm.core.services.interfaces;

import com.hb.crm.core.beans.LiveStream.LiveStreamConfiguration;

import org.springframework.data.domain.Page;

public interface LiveStreamConfigurationService {
    
    /**
     * Get all live stream configurations ordered by creation date (newest first)
     * @return List of all configurations
     */
    Page<LiveStreamConfiguration> getPagedConfigurations( int page, int limit);
    
    /**
     * Create a new configuration and make it active (deactivates all others)
     * @param configuration The new configuration to create
     * @return The created configuration
     */
    LiveStreamConfiguration createConfiguration(LiveStreamConfiguration configuration) ;

    /**
     * Activate a specific AWS IVS configuration (deactivates all others)
     * Only one configuration can be active at a time
     * @param id Configuration ID to activate
     * @return The activated configuration
     */
    LiveStreamConfiguration activateConfiguration(String id);

    /**
     * Deactivate a specific AWS IVS configuration
     * @param id Configuration ID to deactivate
     * @return The deactivated configuration
     */
    LiveStreamConfiguration deactivateConfiguration(String id);

    /**
     * Get the currently active AWS IVS configuration
     * @return The active configuration
     * @throws NotFoundException if no active configuration is found
     */
    LiveStreamConfiguration getActiveConfiguration();
}