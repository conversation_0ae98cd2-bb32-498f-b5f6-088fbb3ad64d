package com.hb.crm.core.repositories;

import com.hb.crm.core.CombinedKeys.SubscribeKey;
import com.hb.crm.core.Enums.FollowMeSubscribeStatus;
import com.hb.crm.core.beans.FollowMe.FollowMeSubscribe;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for FollowMeSubscribe entity
 * 
 * This repository handles database operations for Follow Me subscriptions,
 * which track individual user subscriptions to packages for independent booking.
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public interface FollowMeSubscribeRepository extends MongoRepository<FollowMeSubscribe, SubscribeKey> {

    /**
     * Find all subscriptions by user ID
     * 
     * @param userId The ID of the user
     * @return List of Follow Me subscriptions for the user
     */
    @Query("{ '_id.user.$id': ?0 }")
    List<FollowMeSubscribe> findByUserId(String userId);

    /**
     * Find subscription by user and package
     * 
     * @param userId The ID of the user
     * @param packageId The ID of the package
     * @return Optional Follow Me subscription
     */
    @Query("{ '_id.user.$id': ?0, '_id._package.$id': ?1 }")
    Optional<FollowMeSubscribe> findByUserIdAndPackageId(String userId, String packageId);

    /**
     * Find all subscriptions for a package
     * 
     * @param packageId The ID of the package
     * @return List of Follow Me subscriptions for the package
     */
    @Query("{ '_id._package.$id': ?0 }")
    List<FollowMeSubscribe> findByPackageId(String packageId);

    /**
     * Find subscriptions by status
     * 
     * @param status The subscription status to filter by
     * @return List of Follow Me subscriptions with the specified status
     */
    List<FollowMeSubscribe> findByStatus(FollowMeSubscribeStatus status);

    /**
     * Check if subscription exists for user and package
     * 
     * @param userId The ID of the user
     * @param packageId The ID of the package
     * @return true if subscription exists
     */
    @Query("{ '_id.user.$id': ?0, '_id._package.$id': ?1 }")
    boolean existsByUserIdAndPackageId(String userId, String packageId);
}
