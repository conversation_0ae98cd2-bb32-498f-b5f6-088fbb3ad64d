package com.hb.crm.core.beans.LiveStream;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;


@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Document
public class LiveStreamConfiguration 
{
    @Id
    private String id;
    private String accessKey;
    private String secretKey;
    private String region;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    // for saving the recording on aws
    private String recordingConfigurationArn;

    private boolean active;
}
