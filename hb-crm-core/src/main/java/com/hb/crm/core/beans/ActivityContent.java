package com.hb.crm.core.beans;

import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.DBRef;

import java.util.List;

@Getter
@Setter
public class ActivityContent {
    @Id
    private String id;
    private String name;
    private String details;
    private Double longitude;
    private Double latitude;
    private String locationLink;
    private MediaWrapper mainImage;
    private List<MediaWrapper> medias;
    private Double rate;
    @DBRef
    private ActivityCategory activityCategory;
}
