package com.hb.crm.core.config;

import com.hb.crm.core.Convrter.StateReadConverter;
import com.hb.crm.core.Convrter.StateWriteConverter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.config.AbstractMongoClientConfiguration;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.convert.MongoCustomConversions;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;

import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;

import java.util.Arrays;

@Configuration
@EnableMongoRepositories(basePackages = "com.hb.crm.core.repositories", mongoTemplateRef = "mongoTemplate1")
public class MongoConfig extends AbstractMongoClientConfiguration {

    @Value("${spring.data.mongodb.uri}")
    private String mongoUri1;
    @Value("${spring.data.mongodb.database}")
    private  String dbname;
    @Override
    protected String getDatabaseName() {
        return dbname;
    }

    @Override
    @Bean(name = "MongoClient1")
    public MongoClient mongoClient() {
        return MongoClients.create(mongoUri1);
    }

    @Bean(name = "mongoTemplate1")
    public MongoTemplate mongoTemplate1() {
        return new MongoTemplate(mongoClient(), getDatabaseName());
    }

    @Bean
    public MongoCustomConversions mongoCustomConversions() {
        return new MongoCustomConversions(
                Arrays.asList(
                        new StateReadConverter(),
                        new StateWriteConverter()
                )
        );
    }
}


