package com.hb.crm.core.repositories;

import com.hb.crm.core.Enums.PackageType;
import com.hb.crm.core.beans.Trash.TrashedSubPackage;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface TrashedSubPackageRepository extends MongoRepository<TrashedSubPackage, String> {
    
    /**
     * Find trashed packages by original SubPackage ID
     */
    Optional<TrashedSubPackage> findByOriginalSubPackageId(String originalSubPackageId);
    
    /**
     * Find trashed packages by package type with pagination
     */
    Page<TrashedSubPackage> findByPackageType(PackageType packageType, Pageable pageable);
    
    /**
     * Find all trashed packages with pagination
     */
    Page<TrashedSubPackage> findAllByOrderByDeletedAtDesc(Pageable pageable);
    
    /**
     * Find trashed packages deleted within a date range
     */
    Page<TrashedSubPackage> findByDeletedAtBetween(LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);
    
    /**
     * Performs fuzzy search on trashed packages with pagination.
     * This method uses MongoDB Atlas Search with fuzzy matching capabilities
     * to handle typos and similar-sounding words.
     *
     * @param searchTerm The search query to match against package fields
     * @return A list of TrashedSubPackage objects matching the search criteria
     */
    @Aggregation(pipeline = {
        // Atlas Search stage with fuzzy matching
        "{ " +
            "$search: { " +
                "'index': 'default', " +
                "'compound': { " +
                    "'should': [ " +
                        "{ 'text': { " +
                            "'query': ?0, " +
                            "'path': 'name', " +
                            "'fuzzy': { " +
                                "'maxEdits': 2, " +
                                "'prefixLength': 0, " +
                                "'maxExpansions': 50 " +
                            "} " +
                        "} }, " +
                        "{ 'text': { " +
                            "'query': ?0, " +
                            "'path': 'slug', " +
                            "'fuzzy': { " +
                                "'maxEdits': 2, " +
                                "'prefixLength': 0, " +
                                "'maxExpansions': 50 " +
                            "} " +
                        "} }, " +
                        "{ 'text': { " +
                            "'query': ?0, " +
                            "'path': 'deletionReason', " +
                            "'fuzzy': { " +
                                "'maxEdits': 2, " +
                                "'prefixLength': 0, " +
                                "'maxExpansions': 50 " +
                            "} " +
                        "} } " +
                    "], " +
                    "'minimumShouldMatch': 1 " +
                "} " +
            "} " +
        "} ",
        // Add search score for relevance sorting
        "{ $addFields: { score: { $meta: 'searchScore' } } }",
        // Sort by relevance score and deletion date
        "{ $sort: { score: -1, deletedAt: -1 } }"
    })
    List<TrashedSubPackage> findBySearchTerm(String searchTerm);
    
    /**
     * Fuzzy search with package type filter
     */
    @Aggregation(pipeline = {
        // Atlas Search stage with fuzzy matching
        "{ " +
            "$search: { " +
                "'index': 'default', " +
                "'compound': { " +
                    "'should': [ " +
                        "{ 'text': { " +
                            "'query': ?0, " +
                            "'path': 'name', " +
                            "'fuzzy': { " +
                                "'maxEdits': 2, " +
                                "'prefixLength': 0, " +
                                "'maxExpansions': 50 " +
                            "} " +
                        "} }, " +
                        "{ 'text': { " +
                            "'query': ?0, " +
                            "'path': 'slug', " +
                            "'fuzzy': { " +
                                "'maxEdits': 2, " +
                                "'prefixLength': 0, " +
                                "'maxExpansions': 50 " +
                            "} " +
                        "} }, " +
                        "{ 'text': { " +
                            "'query': ?0, " +
                            "'path': 'deletionReason', " +
                            "'fuzzy': { " +
                                "'maxEdits': 2, " +
                                "'prefixLength': 0, " +
                                "'maxExpansions': 50 " +
                            "} " +
                        "} } " +
                    "], " +
                    "'minimumShouldMatch': 1 " +
                "} " +
            "} " +
        "} ",
        // Filter by package type
        "{ $match: { 'packageType': ?1 } }",
        // Add search score for relevance sorting
        "{ $addFields: { score: { $meta: 'searchScore' } } }",
        // Sort by relevance score and deletion date
        "{ $sort: { score: -1, deletedAt: -1 } }"
    })
    List<TrashedSubPackage> findBySearchTermAndPackageType(String searchTerm, PackageType packageType);
    
    /**
     * Find trashed packages by package type and list of IDs with pagination support
     */
    Page<TrashedSubPackage> findByPackageTypeAndIdIn(PackageType packageType, List<String> ids, Pageable pageable);
    
    /**
     * Find trashed packages by list of IDs with pagination support
     */
    Page<TrashedSubPackage> findByIdIn(List<String> ids, Pageable pageable);
    
    /**
     * Find trashed packages by list of IDs without pagination
     */
    List<TrashedSubPackage> findByIdIn(List<String> ids);
    
    /**
     * Count trashed packages by package type
     */
    long countByPackageType(PackageType packageType);
    
    /**
     * Count all trashed packages
     */
    long count();
    
    /**
     * Check if a trashed package exists by original SubPackage ID
     */
    boolean existsByOriginalSubPackageId(String originalSubPackageId);
}
