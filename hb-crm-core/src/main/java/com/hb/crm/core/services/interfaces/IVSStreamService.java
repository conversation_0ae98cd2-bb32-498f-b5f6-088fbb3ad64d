package com.hb.crm.core.services.interfaces;

import com.hb.crm.core.dtos.LiveStream.LiveStreamDownloadDto;
import com.hb.crm.core.dtos.LiveStream.LiveStreamStartResponseDto;
import java.util.Map;

public interface IVSStreamService {
    /**
     * Create a new IVS channel for an influencer
     * @param influencerId The ID of the influencer
     * @return LiveStreamStartResponseDto containing channel details
     */
    LiveStreamStartResponseDto createChannelForInfluencer(String influencerId);
    
    /**
     * Stop streaming and revoke access to the channel
     * @param channelArn The ARN of the channel to stop
     */
    void stopStream(String channelArn);
    
   
    /**
     * Prepare live stream recording for MP4 conversion (without creating job)
     * @param channelArn The ARN of the channel containing the recording
     * @return Map containing conversion info and download URLs
     */
    Map<String, Object> convertLiveStreamToMp4(String channelArn);

    /**
     * Check if MP4 files exist for a channel and return download URL
     * @param channelId Channel ID for constructing output path
     * @return Map containing MP4 file info and download URL if available
     */
    Map<String, Object> checkMp4Status(String channelId);

    /**
     * Start background MP4 conversion job (non-blocking) - updates LiveStream document
     * @param streamId The stream ID to update
     * @param channelArn The ARN of the channel containing the recording
     */
    void convertToMp4Async(String streamId, String channelArn);

    /**
     * Generate pre-signed URL for S3 object access
     * @param key S3 object key
     * @return Pre-signed URL for download
     */
    java.net.URL grantAccessUrl(String key);

}
