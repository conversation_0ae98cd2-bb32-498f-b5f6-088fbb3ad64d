package com.hb.crm.core.Convrter;

import com.hb.crm.core.Enums.State;
import org.springframework.core.convert.converter.Converter;
import org.springframework.data.convert.ReadingConverter;

@ReadingConverter
public class StateReadConverter implements Converter<Integer, State> {

    @Override
    public State convert(Integer source) {
        System.out.println("Converting value: " + source); // Add logging for debugging
        for (State value : State.values()) {
            if (value.getValue() == source) {
                return value;
            }
        }
        throw new IllegalArgumentException("Unknown enum value: " + source);
    }
}
