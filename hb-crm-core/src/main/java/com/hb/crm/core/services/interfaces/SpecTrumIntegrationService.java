package com.hb.crm.core.services.interfaces;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.hb.crm.core.dtos.spectTrum.FlightResponse.FlghtResponse;
import com.hb.crm.core.dtos.spectTrum.FlightSearch.FlightSearch;
import com.hb.crm.core.dtos.spectTrum.HotelSearch;
import com.hb.crm.core.dtos.spectTrum.HotelResponse.HotelSearchResponseWrapper;
import com.hb.crm.core.dtos.spectTrum.HotelAvailabilityRequest;
import com.hb.crm.core.dtos.spectTrum.HotelAvailabilityResponse.HotelAvailabilityResponse;
import com.hb.crm.core.dtos.spectTrum.FlightConfirmPrice.FlightConfirmPriceRequest;
import com.hb.crm.core.dtos.spectTrum.FlightConfirmPrice.FlightConfirmPriceResponse;
import com.hb.crm.core.dtos.spectTrum.FlightBooking.FlightBookingRequest;
import com.hb.crm.core.dtos.spectTrum.FlightBooking.FlightBookingResponse;
import com.hb.crm.core.dtos.spectTrum.FlightBooking.GetBookingResponse;
import com.hb.crm.core.dtos.spectTrum.FlightBooking.CancelBookingResponse;
import com.hb.crm.core.dtos.spectTrum.HotelConfirmPrice.HotelConfirmPriceRequest;
import com.hb.crm.core.dtos.spectTrum.HotelConfirmPrice.HotelConfirmPriceResponse;
import com.hb.crm.core.dtos.spectTrum.HotelPreBook.HotelPreBookRequest;
import com.hb.crm.core.dtos.spectTrum.HotelPreBook.HotelPreBookResponse;
import com.hb.crm.core.dtos.spectTrum.HotelBooking.HotelBookingRequest;
import com.hb.crm.core.dtos.spectTrum.HotelBooking.HotelBookingResponse;
import com.hb.crm.core.dtos.spectTrum.HotelGetBooking.HotelGetBookingResponse;
import com.hb.crm.core.dtos.spectTrum.HotelCancelBooking.HotelCancelBookingResponse;

public interface SpecTrumIntegrationService 
{
    String authorize() throws JsonProcessingException;
    
    FlghtResponse searchFlights(FlightSearch request) throws JsonProcessingException;
    
    HotelSearchResponseWrapper searchHotels(HotelSearch request) throws JsonProcessingException;

    HotelAvailabilityResponse checkHotelAvailability(HotelAvailabilityRequest request) throws JsonProcessingException;

    /**
     * Confirms flight pricing before booking
     * 
     * @param request Flight confirm price request containing flight offers and criteria
     * @return FlightConfirmPriceResponse containing confirmed pricing and requirements
     * @throws JsonProcessingException if JSON processing fails
     */
    FlightConfirmPriceResponse confirmFlightPrice(FlightConfirmPriceRequest request) throws JsonProcessingException;

    /**
     * Books a flight using the Spectrum API
     * 
     * Creates a flight booking after price confirmation has been completed.
     * Requires a valid confirmPriceReferenceId and complete traveler information.
     * 
     * @param request Flight booking request containing confirm price reference and traveler details
     * @return FlightBookingResponse containing booking confirmation and trip details
     * @throws JsonProcessingException if JSON processing fails
     */
    FlightBookingResponse bookFlight(FlightBookingRequest request) throws JsonProcessingException;

    /**
     * Retrieves booking details using the Spectrum API
     * 
     * Gets detailed booking information for a created booking using the tripId.
     * Returns comprehensive booking details including flight information, passenger details,
     * fare rules, and booking status.
     * 
     * @param tripId The unique trip identifier for the booking
     * @return GetBookingResponse containing detailed booking information
     * @throws JsonProcessingException if JSON processing fails
     */
    GetBookingResponse getBooking(String tripId) throws JsonProcessingException;

    /**
     * Cancels a flight booking using the Spectrum API
     * 
     * Cancels an existing flight booking using the tripId and bookingReferenceId.
     * Returns cancellation status and details.
     * 
     * @param tripId The unique trip identifier for the booking
     * @param bookingReferenceId The booking reference identifier
     * @return CancelBookingResponse containing cancellation status and details
     * @throws JsonProcessingException if JSON processing fails
     */
    CancelBookingResponse cancelBooking(String tripId, String bookingReferenceId) throws JsonProcessingException;

    /**
     * Confirms hotel pricing using the Spectrum API
     * 
     * Validates and confirms hotel pricing details for selected property and room options.
     * Returns detailed property information with confirmed pricing, room options, and policies.
     * 
     * @param request Hotel confirm price request containing property reference and room selections
     * @return HotelConfirmPriceResponse containing confirmed pricing and property details
     * @throws JsonProcessingException if JSON processing fails
     */
    HotelConfirmPriceResponse confirmHotelPrice(HotelConfirmPriceRequest request) throws JsonProcessingException;

    /**
     * Creates a hotel pre-booking summary using the Spectrum API
     * 
     * Checks booking summary before creating the actual hotel booking confirmation.
     * Returns booking reference ID, trip ID, and pre-booking status for final confirmation.
     * 
     * @param request Hotel pre-book request containing guest details and room selections
     * @return HotelPreBookResponse containing booking reference and pre-booking status
     * @throws JsonProcessingException if JSON processing fails
     */
    HotelPreBookResponse hotelPreBookSummary(HotelPreBookRequest request) throws JsonProcessingException;

    /**
     * Creates a confirmed hotel booking using the Spectrum API
     * 
     * Finalizes the hotel booking after pre-booking summary has been completed.
     * Uses the trip ID and booking reference ID from the pre-booking to confirm the final booking.
     * 
     * @param request Hotel booking request containing trip ID and booking reference ID
     * @return HotelBookingResponse containing final booking confirmation and status
     * @throws JsonProcessingException if JSON processing fails
     */
    HotelBookingResponse hotelCreateBooking(HotelBookingRequest request) throws JsonProcessingException;

    /**
     * Retrieves detailed hotel booking information using the Spectrum API
     * 
     * Gets comprehensive hotel booking details including property information, room details,
     * guest information, pricing, policies, and flight details (for package bookings).
     * 
     * @param tripId The unique trip identifier for the hotel booking
     * @return HotelGetBookingResponse containing detailed hotel booking information
     * @throws JsonProcessingException if JSON processing fails
     */
    HotelGetBookingResponse getHotelBooking(String tripId) throws JsonProcessingException;

    /**
     * Cancels a hotel booking using the Spectrum API
     * 
     * Cancels a confirmed hotel booking with optional room-specific cancellation
     * and pre-cancellation reference handling.
     * 
     * @param tripId The unique trip identifier for the hotel booking (required)
     * @param bookingReferenceId The booking reference ID (optional)
     * @param roomIds Comma-separated list of room IDs to cancel (optional - if not provided, cancels entire booking)
     * @param preCancellationReferences Pre-cancellation reference IDs (optional)
     * @return HotelCancelBookingResponse indicating cancellation success or failure
     * @throws JsonProcessingException if JSON processing fails
     */
    HotelCancelBookingResponse cancelHotelBooking(String tripId, String bookingReferenceId, 
                                                  String roomIds, String preCancellationReferences) throws JsonProcessingException;

}
