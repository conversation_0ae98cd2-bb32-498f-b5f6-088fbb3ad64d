package com.hb.crm.core.repositories;

import com.hb.crm.core.Enums.chat.AgentConversationStatus;
import com.hb.crm.core.beans.Employee;
import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.time.LocalDateTime;

@Repository
public interface EmployeeRepository extends MongoRepository<Employee, String> {

    Employee findByUsername(String username);

    Optional<Employee> findByPassResetKey(String passkey);

    List<Employee> findByAgentConversationStatusIn(List<AgentConversationStatus> statuses);

    List<Employee> findByAgentConversationStatusAndLastConnectionDateBefore(
            AgentConversationStatus status, LocalDateTime dateTime);

    @Aggregation(pipeline = {
            "{ $match: { agentConversationStatus: 'ACTIVE' } }",
            "{ $addFields: { queueSize: { $size: '$agentQueue' } } }",
            "{ $match: { queueSize: { $lt: ?0 } } }",
            "{ $sort: { queueSize: 1 } }",
            "{ $limit: 1 }"
    })
    Optional<Employee> findAvailableEmployeeWithMinQueue(int maxQueueSize);

    List<Employee> findByFcmTokensContaining(String fcmToken);
}
