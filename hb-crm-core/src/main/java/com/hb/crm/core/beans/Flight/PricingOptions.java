package com.hb.crm.core.beans.Flight;

import java.util.ArrayList;

public class PricingOptions {
    private ArrayList<String> fareType;
    private boolean includedCheckedBagsOnly;
    private boolean refundableFare;

    public ArrayList<String> getFareType() {
        return fareType;
    }

    public void setFareType(ArrayList<String> fareType) {
        this.fareType = fareType;
    }

    public boolean isIncludedCheckedBagsOnly() {
        return includedCheckedBagsOnly;
    }

    public void setIncludedCheckedBagsOnly(boolean includedCheckedBagsOnly) {
        this.includedCheckedBagsOnly = includedCheckedBagsOnly;
    }

    public boolean isRefundableFare() {
        return refundableFare;
    }

    public void setRefundableFare(boolean refundableFare) {
        this.refundableFare = refundableFare;
    }
}
