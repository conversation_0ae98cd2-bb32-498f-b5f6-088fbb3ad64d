package com.hb.crm.core.beans.Flight;

import com.hb.crm.core.Enums.FlightStep;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class FlightsDirections {

    private String id;
    private  String path;
    private List<Flight> flights;

    private ArrayList<Location> locations;
    private ArrayList<Aircraft> aircrafts;
    private ArrayList<Carrier> carriers;
    private  BigDecimal economyPrice;
    private  BigDecimal childEconomyPrice;
    private  BigDecimal childBusinessPrice;
    private  BigDecimal businessPrice;
    private  String origin;
    private  String destination;
    private  Integer stop;
    private  ArrayList<String> upgradeCabins;
    private FlightStep flightStep;

    public ArrayList<Aircraft> getAircrafts() {
        return aircrafts;
    }

    public void setAircrafts(ArrayList<Aircraft> aircrafts) {
        this.aircrafts = aircrafts;
    }

    public ArrayList<Carrier> getCarriers() {
        return carriers;
    }

    public void setCarriers(ArrayList<Carrier> carriers) {
        this.carriers = carriers;
    }

    public String getOrigin() {
        return origin;
    }

    public void setOrigin(String origin) {
        this.origin = origin;
    }

    public String getDestination() {
        return destination;
    }

    public void setDestination(String destination) {
        this.destination = destination;
    }

    public FlightStep getFlightStep() {
        return flightStep;
    }

    public void setFlightStep(FlightStep flightStep) {
        this.flightStep = flightStep;
    }

    public Integer getStop() {
        return stop;
    }

    public void setStop(Integer stop) {
        this.stop = stop;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public List<Flight> getFlights() {
        return flights;
    }

    public void setFlights(List<Flight> flights) {
        this.flights = flights;
    }

    public BigDecimal getEconomyPrice() {
        return economyPrice;
    }

    public void setEconomyPrice(BigDecimal economyPrice) {
        this.economyPrice = economyPrice;
    }

    public BigDecimal getChildEconomyPrice() {
        return childEconomyPrice;
    }

    public ArrayList<String> getUpgradeCabins() {
        return upgradeCabins;
    }

    public void setUpgradeCabins(ArrayList<String> upgradeCabins) {
        this.upgradeCabins = upgradeCabins;
    }

    public void setChildEconomyPrice(BigDecimal childEconomyPrice) {
        this.childEconomyPrice = childEconomyPrice;
    }

    public BigDecimal getChildBusinessPrice() {
        return childBusinessPrice;
    }

    public void setChildBusinessPrice(BigDecimal childBusinessPrice) {
        this.childBusinessPrice = childBusinessPrice;
    }

    public BigDecimal getBusinessPrice() {
        return businessPrice;
    }

    public void setBusinessPrice(BigDecimal businessPrice) {
        this.businessPrice = businessPrice;
    }

    public ArrayList<Location> getLocations() {
        return locations;
    }

    public void setLocations(ArrayList<Location> locations) {
        this.locations = locations;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
}
