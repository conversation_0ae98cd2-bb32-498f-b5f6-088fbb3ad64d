package com.hb.crm.core.beans.Flight;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.ArrayList;
@JsonIgnoreProperties(ignoreUnknown = true)

public class FareDetailsBySegment {
    private String segmentId;
    private String cabin;
    private String BrandedFare;
    private String fareBasis;
    private IncludedCheckedBags includedCheckedBags;
    private ArrayList<Object> amenities;
    @Field("class")
    @JsonProperty("Class")
    private String myclass;

    public String getSegmentId() {
        return segmentId;
    }

    public void setSegmentId(String segmentId) {
        this.segmentId = segmentId;
    }

    public String getCabin() {
        return cabin;
    }

    public void setCabin(String cabin) {
        this.cabin = cabin;
    }

    public String getFareBasis() {
        return fareBasis;
    }

    public void setFareBasis(String fareBasis) {
        this.fareBasis = fareBasis;
    }

    public IncludedCheckedBags getIncludedCheckedBags() {
        return includedCheckedBags;
    }

    public void setIncludedCheckedBags(IncludedCheckedBags includedCheckedBags) {
        this.includedCheckedBags = includedCheckedBags;
    }

    public ArrayList<Object> getAmenities() {
        return amenities;
    }

    public void setAmenities(ArrayList<Object> amenities) {
        this.amenities = amenities;
    }

    public String getMyclass() {
        return myclass;
    }

    public void setMyclass(String myclass) {
        this.myclass = myclass;
    }

    public String getBrandedFare() {
        return BrandedFare;
    }

    public void setBrandedFare(String brandedFare) {
        this.BrandedFare = brandedFare;
    }
}
