package com.hb.crm.core.beans.LiveStream;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;

import com.hb.crm.core.Enums.LiveStreamStatus;
import com.hb.crm.core.Enums.Mp4ConversionStatus;
import com.hb.crm.core.beans.User;

import java.time.LocalDateTime;
import java.util.List;
import com.hb.crm.core.beans.Tag;

import com.hb.crm.core.beans.Package;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Document
public class LiveStream {
    @Id
    private String id;
    private String title;

    // Enhanced metadata fields
    private String description;
    @DBRef(lazy = true)
    private List<Tag> tags;
 

    @DBRef(lazy = true)
    private User infulancer;
    @DBRef(lazy = true)
    private Package packageRef;

    private String channelArn;
    private String streamKey;
    private String playbackUrl;
    private String ingestEndpoint;

    private LiveStreamStatus status;
    private LocalDateTime createdAt;
    private LocalDateTime endedAt;
    
    private int numberOfReactions = 0;
    private int numberOfComments = 0;
    private int viewersCount = 0;
    
    // MP4 Conversion fields
    private Mp4ConversionStatus mp4ConversionStatus = Mp4ConversionStatus.NOT_STARTED;
    private String mp4Key; // null until conversion finished

}
