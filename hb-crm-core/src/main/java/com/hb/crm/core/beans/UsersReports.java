package com.hb.crm.core.beans;

import com.hb.crm.core.Enums.ReportStatus;
import com.hb.crm.core.Enums.ReportType;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
@Document(collection = "users_reports")
public class UsersReports {
    @Id
    private String id;
    private String description;
    private LocalDateTime created;
    @LastModifiedDate
    private LocalDateTime modified;
    private String entityId;
    private ReportType reportType;
    private ReportStatus status = ReportStatus.PENDING;
    private List<ReportReply> replies = new ArrayList<>();
}

