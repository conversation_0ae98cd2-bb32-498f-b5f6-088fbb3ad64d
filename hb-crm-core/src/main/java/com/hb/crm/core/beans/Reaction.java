package com.hb.crm.core.beans;

import com.hb.crm.core.Enums.ReactionType;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.DBRef;

import java.util.Date;

@Setter
@Getter
public class Reaction {
    @Id
    private String id;
    @DBRef
    private User user;
    @CreatedDate
    private Date createDate;
    @LastModifiedDate
    private Date updateDate;

    private ReactionType reactionType = ReactionType.like;
}
