package com.hb.crm.core.config;

import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.config.AbstractMongoClientConfiguration;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.convert.MongoCustomConversions;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;

import java.util.Arrays;


    @Configuration
    @EnableMongoRepositories(basePackages = "com.hb.crm.core.searchRepositories", mongoTemplateRef = "mongoTemplate2")
    public class SearchMongoConfig extends AbstractMongoClientConfiguration {

        @Value("${spring.data.mongodb.uri2}")
        private String mongoUri2;
        @Value("${spring.data.mongodb.database2}")
        private  String dbname;
        @Override
        protected String getDatabaseName() {
            return dbname;
        }

        @Override
        @Bean(name = "mongoClient2")
        public MongoClient mongoClient() {
            return MongoClients.create(mongoUri2);
        }

        @Bean(name = "mongoTemplate2")
        @Lazy
        public MongoTemplate mongoTemplate2() {
            return new MongoTemplate(mongoClient(), getDatabaseName());
        }

        @Bean(name = "mongoCustomConversions2")
        public MongoCustomConversions mongoCustomConversions() {
            return new MongoCustomConversions(
                    Arrays.asList(
                            new SearchReadConverter(),
                            new SearchWriteConverter()
                    )
            );
        }

    }

