package com.hb.crm.core.beans.FlightPackage;

import lombok.Data;
import org.springframework.data.annotation.Id;

import java.util.List;

@Data
public class Airline {
    @Id
    private String id;
    private String name;
    private List<FlightStep> flightSteps;  // Flight steps for this destination
    private String departureAirport;
    private String arrivalAirport;
    private String carerNames;
}
