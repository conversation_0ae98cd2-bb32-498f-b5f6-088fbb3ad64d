package com.hb.crm.core.services.chat;

import com.hb.crm.core.Enums.*;
import com.hb.crm.core.Enums.chat.*;
import com.hb.crm.core.beans.Employee;
import com.hb.crm.core.beans.SubPackage;
import com.hb.crm.core.beans.User;
import com.hb.crm.core.beans.chat.GroupChat.*;
import com.hb.crm.core.beans.chat.OneChat.AdminConversation;
import com.hb.crm.core.beans.chat.OneChat.ChatMessage;
import com.hb.crm.core.beans.chat.OneChat.Conversation;
import com.hb.crm.core.beans.chat.Poll.Poll;
import com.hb.crm.core.beans.chat.Poll.PollOption;
import com.hb.crm.core.beans.chat.Poll.Vote;
import com.hb.crm.core.dtos.EmployeeDto;
import com.hb.crm.core.dtos.PageDto;
import com.hb.crm.core.dtos.SimpleUserinfoDto;
import com.hb.crm.core.dtos.UserDto;
import com.hb.crm.core.dtos.chat.request.*;
import com.hb.crm.core.dtos.chat.response.*;
import com.hb.crm.core.exceptions.CustomException;
import com.hb.crm.core.repositories.*;
import com.hb.crm.core.repositories.chat.*;
import com.hb.crm.core.services.interfaces.NotificationService;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.hb.crm.core.util.Constants.GROUP_TOPIC_PREFIX;

@Service
@RequiredArgsConstructor
public class ChatMessageServiceImpl implements ChatMessageService {

    private final ModelMapper modelMapper;
    private final UserRepository userRepository;
    private final PollRepository pollRepository;
    private final VoteRepository voteRepository;
    private final QueueChatService queueChatService;
    private final SettingRepository settingRepository;
    private final EmployeeRepository employeeRepository;
    private final SubPackageRepository subPackageRepository;
    private final PollOptionRepository pollOptionRepository;
    private final ChatMessageRepository chatMessageRepository;
    private final ConversationRepository conversationRepository;
    private final GroupChatMessageRepository groupChatMessageRepository;
    private final UserConversationRepository userConversationRepository;
    private final AdminConversationRepository adminConversationRepository;
    private final GroupAdminConversationRepository groupAdminConversationRepository;
    private final GroupConversationRepository groupConversationRepository;
    private final NotificationService notificationService;
    private final TravelerSubscribeRepository travelerSubscribeRepository;

    private final Logger logger = LoggerFactory.getLogger(ChatMessageServiceImpl.class);


    @Override
    @Transactional
    public void persistMessage(ChatMessageRequestDto message) {
        // Ensure we have the conversation
        Conversation conversation = conversationRepository.findById(message.getConversationId())
                .orElseThrow(() -> new CustomException(404, "Conversation not found"));

        if (conversation.getUser() != null) {
            if (!Objects.equals(conversation.getUser().getId(), message.getUserId()))
                throw new CustomException(401, "You can't send message to this conversation!");
        } else {
            logger.error("Data Error, The conversation with id: {} has no user!", conversation.getId());
            throw new CustomException(401, "Data Error, The conversation with id: "
                    + conversation.getId() + "has no user!");
        }

        // Case of update message
        if (message.getId() != null) {
            ChatMessage existingMessage = chatMessageRepository.findById(message.getId())
                    .orElseThrow(() -> new CustomException(404, "The message not found!"));

            existingMessage = createUpdateChatMessage(existingMessage, message,
                    conversation);

            // Update the poll
            if (existingMessage.getType() == ChatMessageType.Poll && isPollMessage(message)) {
                existingMessage.setPoll(updatePoll(existingMessage.getPoll(), message.getPoll()));
            }

            chatMessageRepository.save(existingMessage);
        }

        // Case of a new message
        else {
            // Create and map a new ChatMessage object
            ChatMessage newChatMessage = createUpdateChatMessage(null, message,
                    conversation);

            // Process the poll if present
            if (isPollMessage(message)) {
                addPollToMessage(newChatMessage, message);
            }

            chatMessageRepository.save(newChatMessage);


            // Send notification to user to inform him with message
            User user = conversation.getUser();
            var _package = conversation.getPackage();

            NotificationType type = NotificationType.ChatMessage;
            if (newChatMessage.getType() == ChatMessageType.Poll)
                type = NotificationType.ChatMessagePoll;
            if (newChatMessage.getType() == ChatMessageType.Video)
                type = NotificationType.ChatMessageVideo;
            if (newChatMessage.getType() == ChatMessageType.Voice)
                type = NotificationType.ChatMessageAudio;
            if (newChatMessage.getType() == ChatMessageType.Audio)
                type = NotificationType.ChatMessageAudio;
            if (newChatMessage.getType() == ChatMessageType.Doc)
                type = NotificationType.ChatMessageFile;
            if (newChatMessage.getType() == ChatMessageType.Image)
                type = NotificationType.ChatMessageImage;

            notificationService.sendAndStoreNotification(conversation.getId(),
                    type,
                    user,
                    List.of(message, user),
                    _package != null ? _package.get_package().getInfulancer().getProfileImage() : null,
                    null,
                    NotificationEntityType.CONVERSATION,
                    null,
                    null,
                    user.getUsername(), null);
        }
    }

    @Override
    @Transactional
    public void persistGroupMessage(GroupChatMessageRequestDto message) {
        // Ensure we have the conversation
        GroupConversation conversation = groupConversationRepository.findById(message.getConversationId())
                .orElseThrow(() -> new CustomException(404, "Conversation not found"));

        // Find UserConversation record for this user and conversation
        UserConversation userConversation = userConversationRepository.findByUserIdAndConversationId(
                message.getUserId(),
                conversation.getId()
        ).orElseThrow(() -> new CustomException(403, "User is not a member of this conversation"));

        // Case of update message
        if (message.getId() != null) {
            GroupChatMessage existingMessage = groupChatMessageRepository.findById(message.getId())
                    .orElseThrow(() -> new CustomException(404, "The message not found!"));

            existingMessage = createUpdateGroupChatMessage(existingMessage, message,
                    conversation, userConversation);

            // Update the poll
            if (existingMessage.getType() == ConversationMessageType.Poll && isPollMessage(message)) {
                existingMessage.setPoll(updatePoll(existingMessage.getPoll(), message.getPoll()));
            }

            groupChatMessageRepository.save(existingMessage);
        }

        // Case of a new message
        else {
            // Create and map a new ChatMessage object
            GroupChatMessage newChatMessage = createUpdateGroupChatMessage(null, message,
                    conversation, userConversation);

            // Process the poll if present
            if (isPollMessage(message)) {
                addPollToGroupMessage(newChatMessage, message);
            }

            List<UserConversation> members = userConversationRepository
                    .findByConversationIdAndEndTimeIsNull(conversation.getId());

            newChatMessage.setUserGroupChatMessages(members.stream().map(member -> {
                UserGroupChatMessage userMessageStatus = new UserGroupChatMessage();
                userMessageStatus.setUser(modelMapper.map(member.getUser(), SimpleUserinfoDto.class));
                userMessageStatus.setStatus(ConversationMessageStatus.Pending);
                return userMessageStatus;
            }).collect(Collectors.toList()));

            groupChatMessageRepository.save(newChatMessage);

            // Send notification to user to inform him with a message
            NotificationType type = NotificationType.ChatMessage;
            if (newChatMessage.getType() == ConversationMessageType.Poll)
                type = NotificationType.ChatMessagePoll;
            if (newChatMessage.getType() == ConversationMessageType.Video)
                type = NotificationType.ChatMessageVideo;
            if (newChatMessage.getType() == ConversationMessageType.Voice)
                type = NotificationType.ChatMessageAudio;
            if (newChatMessage.getType() == ConversationMessageType.Audio)
                type = NotificationType.ChatMessageAudio;
            if (newChatMessage.getType() == ConversationMessageType.Doc)
                type = NotificationType.ChatMessageFile;
            if (newChatMessage.getType() == ConversationMessageType.Image)
                type = NotificationType.ChatMessageImage;

            List<UserConversation> userConversations = userConversationRepository
                    .findByConversationIdAndEndTimeIsNull(userConversation.getConversation().getId());
            for (UserConversation userConversation1 : userConversations) {
                if (!Objects.equals(userConversation1.getId(), userConversation.getId())) {
                    // Send notification to user to inform him with message
                    User user = userConversation1.getUser();
                    var _package = conversation.getPackage();
                    notificationService.sendAndStoreNotification(userConversation1.getId(),
                            type,
                            user,
                            List.of(message, user, _package),
                            _package.get_package().getInfulancer().getProfileImage(),
                            userConversation.getUser().getProfileImage(),
                            NotificationEntityType.GROUP_CONVERSATION,
                            null,
                            null,
                            user.getUsername(), null);
                    ;
                }
            }

        }
    }


    @Override
    @Transactional
    public void voteChatMessage(VoteMessageRequestDto voteRequest) {
        // Ensure we have the conversation
        Conversation conversation = conversationRepository.findById(voteRequest.getConversationId())
                .orElseThrow(() -> new CustomException(404, "Conversation not found"));

        if (conversation.getUser() != null) {
            if (!Objects.equals(conversation.getUser().getId(), voteRequest.getUserId()))
                throw new CustomException(401, "You can't send message to this conversation!");
        } else {
            logger.error("Data Error, The conversation with id: {} has no user!", conversation.getId());
            throw new CustomException(401, "Data Error, The conversation with id: "
                    + conversation.getId() + "has no user!");
        }

        votePollOption(voteRequest.getVote(), conversation, conversation.getUser());
    }

    @Override
    @Transactional
    public void voteGroupChatMessage(VoteMessageRequestDto voteRequest) {
        GroupConversation conversation = groupConversationRepository.findById(voteRequest.getConversationId())
                .orElseThrow(() -> new CustomException(404, "Conversation not found"));

        // Find UserConversation record for this user and conversation
        UserConversation userConversation = userConversationRepository.findByUserIdAndConversationId(
                voteRequest.getUserId(),
                conversation.getId()
        ).orElseThrow(() -> new CustomException(403, "User is not a member of this conversation"));

        votePollOption(voteRequest.getVote(), conversation, userConversation.getUser());
    }

    @Override
    @Transactional
    public void retractVoteGroupChatMessage(VoteMessageRequestDto voteRequest) {
        GroupConversation conversation = groupConversationRepository.findById(voteRequest.getConversationId())
                .orElseThrow(() -> new CustomException(404, "Conversation not found"));

        // Find UserConversation record for this user and conversation
        UserConversation userConversation = userConversationRepository.findByUserIdAndConversationId(
                voteRequest.getUserId(),
                conversation.getId()
        ).orElseThrow(() -> new CustomException(403, "User is not a member of this conversation"));

        RetractVotePollOption(voteRequest.getVote(), conversation, userConversation.getUser());
    }


    @Override
    @Transactional
    public void updateChatMessageStatus(UpdateChatMessageStatusRequest updateStatusRequest) {
        ChatMessage message = chatMessageRepository.findById(updateStatusRequest.getMessageId())
                .orElseThrow(() -> new CustomException(404, "Message not found!"));
        message.setStatus(updateStatusRequest.getStatus());
        chatMessageRepository.save(message);
    }

    @Override
    @Transactional
    public void updateGroupChatMessageStatus(UpdateGroupChatMessageStatusRequest updateStatusRequest) {
        GroupChatMessage message = groupChatMessageRepository.findById(updateStatusRequest.getMessageId())
                .orElseThrow(() -> new CustomException(404, "Message not found!"));

        User user = userRepository.findById(updateStatusRequest.getUserId())
                .orElseThrow(() -> new CustomException(404, "User not found!"));

        for (UserGroupChatMessage userMessage : message.getUserGroupChatMessages()) {
            if (userMessage.getUser().getId().equals(user.getId())) {
                userMessage.setStatus(updateStatusRequest.getStatus());
                groupChatMessageRepository.save(message);
                return;
            }
        }

        UserGroupChatMessage userMessageStatus = new UserGroupChatMessage();
        userMessageStatus.setUser(modelMapper.map(user, SimpleUserinfoDto.class));
        userMessageStatus.setStatus(updateStatusRequest.getStatus());

        message.getUserGroupChatMessages().removeIf(userMessage -> userMessage.getUser().getId().equals(user.getId()));
        message.getUserGroupChatMessages().add(userMessageStatus);

        groupChatMessageRepository.save(message);
    }

    @Override
    @Transactional
    public GroupConversation createGroupConversation(String subPackageId) {
        // Check if group conversation already exists for this package
        List<GroupConversation> existingConversation = groupConversationRepository.findByPackageId(subPackageId);

        if (existingConversation != null && !existingConversation.isEmpty()) {
            System.out.println("HighLight conversation already exists for this package");
            return existingConversation.getFirst();
        }

        SubPackage subPackage = subPackageRepository.findById(subPackageId)
                .orElseThrow(() -> new CustomException(404, "Package not found"));

        GroupConversation groupConversation = new GroupConversation();
        groupConversation.setPackage(subPackage);
        groupConversation.setTopic(GROUP_TOPIC_PREFIX + subPackage.getId());

        return groupConversationRepository.save(groupConversation);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ChatMessageResponseDto> getConversationHistory(String conversationId, int page, int size, List<ChatMessageType> types) {
        Page<ChatMessage> result;
        if (types != null && !types.isEmpty()) {
            result = chatMessageRepository.findByConversationIdAndTypesAndDateTimeBefore(
                    conversationId,
                    types,
                    PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "dateTime"))
            );
        } else {
            result = chatMessageRepository.findByConversationIdAndDateTimeBefore(
                    conversationId,
                    PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "dateTime"))
            );
        }

        return result.map(message -> modelMapper.map(message, ChatMessageResponseDto.class));
    }

    @Override
    @Transactional(readOnly = true)
    public Page<GroupChatMessageResponseDto> getGroupConversationHistory(String conversationId, int page, int size, List<ConversationMessageType> types) {
        Page<GroupChatMessage> result;
        if (types != null && !types.isEmpty()) {
            result = groupChatMessageRepository.findByConversationIdAndTypesAndDateTimeBefore(
                    conversationId,
                    types,
                    PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "dateTime"))
            );
        } else {
            result = groupChatMessageRepository.findByConversationIdAndDateTimeBefore(
                    conversationId,
                    PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "dateTime"))
            );
        }

        return result.map(message -> modelMapper.map(message, GroupChatMessageResponseDto.class));
    }


    @Override
    @Transactional
    public UserConversationWithMassagesDto addUserToGroupConversation(String conversationId, String userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new CustomException(404, "User not found!"));
        GroupConversation conversation = groupConversationRepository.findById(conversationId)
                .orElseThrow(() -> new CustomException(404, "Conversation not found!"));


        var existingUserConversation = userConversationRepository.findByUserIdAndConversationId(userId, conversationId);
        if (existingUserConversation.isPresent()) {
//            if (existingUserConversation.get().getEndTime() != null) {
            existingUserConversation.get().setEndTime(null);
            userConversationRepository.save(existingUserConversation.get());

            UserConversationWithMassagesDto response = new UserConversationWithMassagesDto();
            response.setConversation(modelMapper.map(conversation, UserConversationResponseDto.class));
            response.setMessages(getGroupConversationHistory(conversationId, 0, 20, null));

            return response;

//            } else {
//                throw new CustomException(400, "User is already a member of this conversation");
//            }
        }

        // TODO: add check for user subscription

//        List<TravelerSubscribe> travelerList = travelerSubscribeRepository
//                .findTravelerSubscribesByUserId(new ObjectId(userId));
//
//        travelerList
//                .stream()
//                .filter(item ->
//                        Objects.equals(item.getSubscribe().getId().get_package().getId(), conversation.getPackage().getId()))
//                .findFirst()
//                .orElseThrow(() -> new CustomException(400, "User is not subscribed to this package"));

        UserConversation userConversation = new UserConversation();
        userConversation.setUser(user);
        userConversation.setConversation(conversation);
        userConversation.setStartTime(LocalDateTime.now());

        userConversationRepository.save(userConversation);
        UserConversationWithMassagesDto response = new UserConversationWithMassagesDto();
        response.setConversation(modelMapper.map(conversation, UserConversationResponseDto.class));
        response.setMessages(getGroupConversationHistory(conversationId, 0, 20, null));

        return response;
    }

    @Override
    @Transactional
    public void removeUserFromGroupConversation(String conversationId, String userId) {
        userRepository.findById(userId)
                .orElseThrow(() -> new CustomException(404, "User not found!"));
        groupConversationRepository.findById(conversationId)
                .orElseThrow(() -> new CustomException(404, "Conversation not found!"));

        var userConversation = userConversationRepository.findByUserIdAndConversationId(userId, conversationId)
                .orElseThrow(() -> new CustomException(404, "User is not a member of this conversation"));

        userConversation.setEndTime(LocalDateTime.now());
        userConversationRepository.save(userConversation);
    }

//    @Override
//    @Transactional
//    public void updateMessageStatus(String messageId, ChatMessageStatus status) {
//        ChatMessage message = chatMessageRepository.findById(messageId)
//                .orElseThrow(() -> new CustomException(404, "Message not found!"));
//        message.setStatus(status);
//        chatMessageRepository.save(message);
//    }

    @Override
    @Transactional(readOnly = true)
    public PageDto<EmployeeDto> getGroupAdmins(String conversationId, boolean includeInactive, int page, int size) {
        GroupConversation conversation = groupConversationRepository.findById(conversationId)
                .orElseThrow(() -> new CustomException(404, "Conversation not found"));

        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "startTime"));

        // Get paginated admins based on includeInactive parameter
        Page<GroupAdminConversation> adminConversationsPage = includeInactive ?
                groupAdminConversationRepository.findByConversation(conversation, pageable) :
                groupAdminConversationRepository.findByConversationAndEndTimeIsNull(conversation, pageable);

        // Convert to DTOs
        List<EmployeeDto> adminDtos = adminConversationsPage.getContent().stream()
                .map(adminConv -> modelMapper.map(adminConv.getEmployee(), EmployeeDto.class))
                .collect(Collectors.toList());

        // Create PageDto
        PageDto<EmployeeDto> pageDto = new PageDto<>();
        pageDto.setItems(adminDtos);
        pageDto.setTotalNoOfItems(adminConversationsPage.getTotalElements());
        pageDto.setPageNumber(page);
        pageDto.setItemsPerPage(size);

        return pageDto;
    }

    @Override
    @Transactional
    public UserConversationWithMassagesDto addAdminToGroupConversation(String conversationId, String employeeId) {
        GroupConversation conversation = groupConversationRepository.findById(conversationId)
                .orElseThrow(() -> new CustomException(404, "Conversation not found"));

        Employee employee = employeeRepository.findById(employeeId)
                .orElseThrow(() -> new CustomException(404, "Employee not found"));

        // Check if employee is already an active admin
        boolean isAlreadyAdmin = groupAdminConversationRepository
                .findByConversationAndEmployeeAndEndTimeIsNull(conversation, employee)
                .isPresent();

        if (isAlreadyAdmin) {
            throw new CustomException(400, "Employee is already an admin of this conversation");
        }

        GroupAdminConversation adminConversation = new GroupAdminConversation();
        adminConversation.setEmployee(employee);
        adminConversation.setConversation(conversation);
        adminConversation.setStartTime(LocalDateTime.now());
        groupAdminConversationRepository.save(adminConversation);

        UserConversationWithMassagesDto response = new UserConversationWithMassagesDto();
        response.setConversation(modelMapper.map(conversation, UserConversationResponseDto.class));
        response.setMessages(getGroupConversationHistory(conversationId, 0, 20, null));

        return response;
    }

    @Override
    @Transactional
    public void removeAdminFromGroupConversation(String conversationId, String employeeId) {
        GroupConversation conversation = groupConversationRepository.findById(conversationId)
                .orElseThrow(() -> new CustomException(404, "Conversation not found"));

        Employee employee = employeeRepository.findById(employeeId)
                .orElseThrow(() -> new CustomException(404, "Employee not found"));

        GroupAdminConversation adminConversation = groupAdminConversationRepository
                .findByConversationAndEmployeeAndEndTimeIsNull(conversation, employee)
                .orElseThrow(() -> new CustomException(400, "Employee is not an admin of this conversation"));

        adminConversation.setEndTime(LocalDateTime.now());
        groupAdminConversationRepository.save(adminConversation);
    }

    @Override
    @Transactional(readOnly = true)
    public PageDto<UserDto> getGroupMembers(String conversationId, boolean includeInactive, int page, int size) {
        // Ensure conversation exists
        GroupConversation conversation = groupConversationRepository.findById(conversationId)
                .orElseThrow(() -> new CustomException(404, "Conversation not found"));

        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "endTime"));

        // Get paginated user conversations based on includeInactive parameter
        Page<UserConversation> userConversationsPage = includeInactive ?
                userConversationRepository.findByConversationId(conversationId, pageable) :
                userConversationRepository.findByConversationIdAndEndTimeIsNull(conversationId, pageable);

        // Convert to UserDto list
        List<UserDto> memberDtos = userConversationsPage.getContent().stream()
                .map(userConv -> modelMapper.map(userConv.getUser(), UserDto.class))
                .collect(Collectors.toList());

        // Create PageDto
        PageDto<UserDto> pageDto = new PageDto<>();
        pageDto.setItems(memberDtos);
        pageDto.setTotalNoOfItems(userConversationsPage.getTotalElements());
        pageDto.setPageNumber(page);
        pageDto.setItemsPerPage(size);

        return pageDto;
    }

    @Override
    @Transactional
    public void muteGroup(String userConversationId, boolean mute) {
        UserConversation userConversation = userConversationRepository.findById(userConversationId)
                .orElseThrow(() -> new CustomException(404, "User conversation not found!"));

        userConversation.setMuted(mute);

        userConversationRepository.save(userConversation);
    }

    @Override
    @Transactional
    public void deleteMessage(List<String> messageId, String userId, DeletionType type) {
        List<ChatMessage> chatMessages = chatMessageRepository.findAllById(messageId);

        for (ChatMessage message : chatMessages) {
            if (!Objects.equals(message.getUser().getId(), userId))
                throw new CustomException(401, "You can't delete messages that you didn't send!");

            if (message.getDateTime().isBefore(LocalDateTime.now().minusMinutes(30)))
                throw new CustomException(400, "You can't delete messages that are older than 30 minutes!");
        }

        if (type == DeletionType.ForAll) {
            chatMessageRepository.deleteAllById(chatMessages.
                    stream()
                    .map(ChatMessage::getId)
                    .toList());
        } else if (type == DeletionType.ForMe) {
            for (ChatMessage message : chatMessages) {
                message.setDeletedByMe(true);
                chatMessageRepository.save(message);
            }
        }
    }

    @Override
    @Transactional
    public void deleteGroupMessage(List<String> messageId, String userId, DeletionType type) {
        List<GroupChatMessage> chatMessages = groupChatMessageRepository.findAllById(messageId);

        for (GroupChatMessage message : chatMessages) {
            if (!Objects.equals(message.getUser().getId(), userId))
                throw new CustomException(400, "You can't delete messages that you didn't send!");

            if (message.getDateTime().isBefore(LocalDateTime.now().minusMinutes(30)))
                throw new CustomException(400, "You can't delete messages that are older than 30 minutes!");
        }

        if (type == DeletionType.ForAll) {
            chatMessageRepository.deleteAllById(chatMessages.
                    stream()
                    .map(GroupChatMessage::getId)
                    .toList());
        } else if (type == DeletionType.ForMe) {
            for (GroupChatMessage message : chatMessages) {
                message.setDeletedByMe(true);
                groupChatMessageRepository.save(message);
            }
        }
    }

    @Override
    @Transactional
    public ConversationDto createConversation(String userId, String packageId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new CustomException(404, "User not found"));

        if (packageId != null) {
            SubPackage subPackage = subPackageRepository.findById(packageId)
                    .orElseThrow(() -> new CustomException(404, "Package not found"));
            Conversation conversation = new Conversation();
            if (subPackage.getPackageType() == PackageType.TravelWithMe)
                conversation.setType(ConversationTypes.TWM);
            else if (subPackage.getPackageType() == PackageType.FollowMe) {
                conversation.setType(ConversationTypes.FollowMe);
            }
            conversation.setTopic(packageId);
            conversation.setUser(user);

            return modelMapper.map(conversationRepository.save(conversation), ConversationDto.class);
        }

        Conversation conversation = new Conversation();
        conversation.setType(ConversationTypes.General);
        conversation.setTopic(user.getId() + "-" + UUID.randomUUID());
        conversation.setUser(user);
        conversation.setCreatedAt(LocalDateTime.now());

        var result = modelMapper.map(conversationRepository.save(conversation), ConversationDto.class);

        // Find available agent who is online and have minimum number of queued chats
        var availableAgentOpt = employeeRepository.findAvailableEmployeeWithMinQueue(Integer.parseInt(settingRepository
                .findByName("MaxAgentQueueSizeForMainQueue").getValue()));

        // Assign chat automatically to online agent that have minimum number of queued chats
        if (availableAgentOpt.isPresent()) {
            assignChatToAgent(result, availableAgentOpt.get().getId());
        }

        // If no agent is available then put the chat in the main queue
        else {
            queueChatService.enqueueConversationByTimestamp(result);
        }

        return result;
    }

    @Override
    @Transactional
    public ConversationDto updateConversationSettings(String conversationId, Boolean isMuted, Boolean isClosed) {
        Conversation conversation = conversationRepository.findById(conversationId)
                .orElseThrow(() -> new CustomException(404, "Conversation not found"));
        if (isMuted != null)
            conversation.setMuted(isMuted);

        if (isClosed != null) {

            if (isClosed) {
                List<AdminConversation> adminConversations = adminConversationRepository
                        .findByConversationIdAndEndTimeIsNull(conversationId);

                adminConversations.forEach(adminConversation -> {
                    adminConversation.setEndTime(LocalDateTime.now());
                    adminConversationRepository.save(adminConversation);

                    queueChatService
                            .dequeueSpecificConversationFromAgentQueue(adminConversation.getEmployee().getId(),
                                    modelMapper.map(conversation, ConversationDto.class));
                });
            }

            if (conversation.isClosed() && !isClosed) {

            }

            conversation.setClosed(isClosed);
        }


        Conversation updatedConversation = conversationRepository.save(conversation);
        return modelMapper.map(updatedConversation, ConversationDto.class);
    }


    @Override
    @Transactional(readOnly = true)
    public long countUnreadChatMessagesByConversation(String conversationId, String userId) {
        // Verify the conversation exists
        conversationRepository.findById(conversationId)
                .orElseThrow(() -> new CustomException(404, "Conversation not found"));

        // Verify user is a participant in the conversation
        if (!conversationRepository.isUserParticipant(conversationId, userId)) {
            throw new CustomException(403, "User is not a participant in this conversation");
        }

        return chatMessageRepository.countUnreadMessagesByConversationAndUserId(conversationId, userId);
    }

    @Override
    @Transactional(readOnly = true)
    public long countAllUnreadChatMessages(String userId) {
        // Verify user exists
        userRepository.findById(userId)
                .orElseThrow(() -> new CustomException(404, "User not found"));

        return chatMessageRepository.countAllUnreadMessagesByUserId(userId);
    }

    @Override
    @Transactional(readOnly = true)
    public long countUnreadGroupMessagesByConversation(String conversationId, String userId) {
        // Verify the conversation exists
        GroupConversation conversation = groupConversationRepository.findById(conversationId)
                .orElseThrow(() -> new CustomException(404, "Conversation not found"));

        // Verify user is a member of the conversation
        userConversationRepository.findByUserIdAndConversationId(
                userId,
                conversation.getId()
        ).orElseThrow(() -> new CustomException(403, "User is not a member of this conversation"));

        return groupChatMessageRepository.countUnreadMessagesByConversationAndUserId(conversationId, userId);
    }

    @Override
    @Transactional(readOnly = true)
    public long countAllUnreadGroupMessages(String userId) {
        // Verify user exists
        userRepository.findById(userId)
                .orElseThrow(() -> new CustomException(404, "User not found"));

        return groupChatMessageRepository.countAllUnreadMessagesByUserId(userId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ChatWithUnreadCountDto> getAllChatsWithUnreadCount(String userId) {
        List<ChatWithUnreadCountDto> result = new ArrayList<>();

        // Get user's one-to-one conversations
        List<Conversation> oneToOneChats = conversationRepository.findByUserId(userId);
        for (Conversation chat : oneToOneChats) {
            ChatWithUnreadCountDto dto = new ChatWithUnreadCountDto();
            dto.setConversationId(chat.getId());
            dto.setTopic(chat.getTopic());
            dto.setMuted(chat.isMuted());
            dto.setClosed(chat.isClosed());
            dto.setUnreadCount(chatMessageRepository.countUnreadMessagesByConversationAndUserId(
                    chat.getId(), userId));

            // Get the last message for this conversation
            ChatMessage lastMessage = chatMessageRepository.findTopByConversationIdAndDeletedByMeFalseOrderByDateTimeDesc(chat.getId());
            if (lastMessage != null) {
                dto.setLastMessage(modelMapper.map(lastMessage, ChatMessageResponseDto.class));
            }

            result.add(dto);
        }

        return result;
    }


    @Override
    @Transactional(readOnly = true)
    public PageDto<MessageSearchResultDto> searchMessages(
            String searchText,
            LocalDateTime startDate,
            LocalDateTime endDate,
            int page,
            int size
    ) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "dateTime"));

        Page<ChatMessage> oneToOneMessages;
        if (startDate == null || endDate == null)
            // Search in one-to-one chats
            oneToOneMessages = chatMessageRepository.searchMessages(searchText, pageable);
        else
            // Search in one-to-one chats
            oneToOneMessages = chatMessageRepository.searchMessagesWithDateFilter(searchText, startDate, endDate, pageable
            );

        // Combine and convert results
        List<MessageSearchResultDto> combinedResults = new ArrayList<>();

        // Convert one-to-one chat messages
        oneToOneMessages.getContent().forEach(message -> {
            MessageSearchResultDto dto = new MessageSearchResultDto();
            dto.setMessageId(message.getId());
            dto.setText(message.getText());
            dto.setDateTime(message.getDateTime());
            dto.setType(message.getType());
            dto.setUser(modelMapper.map(message.getUser(), SimpleUserinfoDto.class));

            MessageSearchResultDto.ConversationInfoDto conversationInfo = new MessageSearchResultDto.ConversationInfoDto();
            conversationInfo.setId(message.getConversation().getId());
            conversationInfo.setTopic(message.getConversation().getTopic());
            conversationInfo.setUser(modelMapper.map(message.getConversation().getUser(), SimpleUserinfoDto.class));

            // Get admin conversations
            List<AdminConversation> adminConversations = adminConversationRepository
                    .findByConversationId(message.getConversation().getId());
            conversationInfo.setAdminConversations(adminConversations.stream()
                    .map(admin -> modelMapper.map(admin, AdminConversationDto.class))
                    .collect(Collectors.toList()));

            dto.setConversation(conversationInfo);
            combinedResults.add(dto);
        });


        // Sort combined results by dateTime
        combinedResults.sort((a, b) -> b.getDateTime().compareTo(a.getDateTime()));

        // Calculate total elements
        long totalElements = oneToOneMessages.getTotalElements();

        return new PageDto<>(
                size,
                totalElements,
                page,
                combinedResults.subList(0, Math.min(size, combinedResults.size()))
        );
    }


    @Override
    @Transactional(readOnly = true)
    public PageDto<ChatWithUnreadCountDto> searchConversations(
            LocalDateTime startDate,
            LocalDateTime endDate,
            String readStatus,
            int page,
            int size,
            String sortDirection
    ) {
        // Set default sort direction if not provided
        Sort.Direction direction = "asc".equalsIgnoreCase(sortDirection) ?
                Sort.Direction.ASC : Sort.Direction.DESC;

        // If dates not provided, use reasonable defaults
        LocalDateTime effectiveStartDate = startDate != null ? startDate : LocalDateTime.now().minusYears(1);
        LocalDateTime effectiveEndDate = endDate != null ? endDate : LocalDateTime.now();

        // Get all conversations
        List<Conversation> allConversations = conversationRepository.findAll();
        List<ChatWithUnreadCountDto> results = new ArrayList<>();

        for (Conversation conversation : allConversations) {
            // Get the last message for this conversation
            ChatMessage lastMessage = chatMessageRepository.findTopByConversationIdAndDeletedByMeFalseOrderByDateTimeDesc(conversation.getId());

            // Skip if no messages or last message is outside date range
            if (lastMessage == null ||
                    lastMessage.getDateTime().isBefore(effectiveStartDate) ||
                    lastMessage.getDateTime().isAfter(effectiveEndDate)) {
                continue;
            }

            // Get unread count for this conversation (for any user)
            long unreadCount = chatMessageRepository.countByConversationIdAndStatusNotAndDeletedByMeFalse(
                    conversation.getId(), ChatMessageStatus.Read.toString());

            // Filter by read status if specified
            if (readStatus != null) {
                boolean isUnread = unreadCount > 0;
                if ("read".equalsIgnoreCase(readStatus) && isUnread) {
                    continue;
                }
                if ("unread".equalsIgnoreCase(readStatus) && !isUnread) {
                    continue;
                }
            }

            // Create DTO
            ChatWithUnreadCountDto dto = new ChatWithUnreadCountDto();
            dto.setConversationId(conversation.getId());
            dto.setTopic(conversation.getTopic());
            dto.setMuted(conversation.isMuted());
            dto.setClosed(conversation.isClosed());
            dto.setUnreadCount(unreadCount);
            dto.setLastMessage(modelMapper.map(lastMessage, ChatMessageResponseDto.class));

            results.add(dto);
        }

        // Sort results by last message date
        results.sort((a, b) -> {
            LocalDateTime dateA = a.getLastMessage().getDateTime();
            LocalDateTime dateB = b.getLastMessage().getDateTime();
            return direction == Sort.Direction.ASC ?
                    dateA.compareTo(dateB) : dateB.compareTo(dateA);
        });

        // Paginate results
        int start = page * size;
        int end = Math.min(start + size, results.size());
        List<ChatWithUnreadCountDto> pagedResults = start < results.size() ?
                results.subList(start, end) : new ArrayList<>();

        return new PageDto<>(
                size,
                results.size(),
                page,
                pagedResults
        );
    }


    @Override
    @Transactional
    public void closeConversationForAgent(String conversationId, String agentId) {
        Conversation conversation = conversationRepository.findById(conversationId)
                .orElseThrow(() -> new CustomException(404, "Conversation not found"));

        Employee agent = employeeRepository.findById(agentId)
                .orElseThrow(() -> new CustomException(404, "Agent not found!"));

        Optional<AdminConversation> adminConversationOpt = adminConversationRepository
                .findByConversationIdAndEmployeeId(conversationId, agent.getId());

        if (adminConversationOpt.isEmpty()) {
            throw new CustomException(400, "Agent is not an admin of this conversation");
        }

        // Set the end time for the admin conversation
        AdminConversation adminConversation = adminConversationOpt.get();
        adminConversation.setEndTime(LocalDateTime.now());
        adminConversationRepository.save(adminConversation);

        // remove the conversation form the agent queue
        queueChatService
                .dequeueSpecificConversationFromAgentQueue(adminConversation.getEmployee().getId(),
                        modelMapper.map(conversation, ConversationDto.class));

        // Set the conversation closed
        conversation.setClosed(true);
        conversationRepository.save(conversation);

        if(agent.getAgentQueue().size() < Integer.parseInt(settingRepository
                .findByName("MaxAgentQueueSizeForMainQueue").getValue())) {
            consumeChatFromQueue(agentId);
        }
    }


    @Override
    @Transactional
    public void openConversationForAgent(String conversationId, String agentId) {
        Conversation conversation = conversationRepository.findById(conversationId)
                .orElseThrow(() -> new CustomException(404, "Conversation not found"));

        Employee agent = employeeRepository.findById(agentId)
                .orElseThrow(() -> new CustomException(404, "Agent not found!"));

        var adminConversationForAgentOpt = adminConversationRepository
                .findByConversationIdAndEmployeeId(conversationId, agentId);

        if(agent.getAgentQueue().size() >= Integer.parseInt(settingRepository
                .findByName("MaxAgentQueueSizeForMainQueue").getValue())) {
            throw new CustomException(400, "Agent queue is full!");
        }

        // Check if the agent already opened that chat before
        if (adminConversationForAgentOpt.isPresent()) {
            var adminConversationAgent = adminConversationForAgentOpt.get();

            // If the chat is still open for the agent
            if (adminConversationAgent.getEndTime() == null) {
                throw new CustomException(400, "Chat already opened for this agent!");
            }

            adminConversationAgent.setEndTime(null);
            adminConversationRepository.save(adminConversationAgent);

            // Add to agent queue
            queueChatService.assignChatToAgent(
                    modelMapper.map(conversation, ConversationDto.class),
                    agent.getId()
            );

            // Ensure conversation is marked open
            conversation.setClosed(false);
            conversationRepository.save(conversation);

            return;
        }

        // Check if there's already an active admin conversation
        List<AdminConversation> alreadyActive = adminConversationRepository
                .findByConversationId(conversationId)
                .stream()
                .filter(ac -> ac.getEndTime() == null)
                .toList();


        if (!alreadyActive.isEmpty()) {
            throw new CustomException(400, "Chat already opened for another agent!");
        }

        // Create new admin conversation
        AdminConversation adminConversation = new AdminConversation();
        adminConversation.setConversation(conversation);
        adminConversation.setEmployee(agent);
        adminConversation.setStartTime(LocalDateTime.now());
        adminConversation.setEndTime(null);
        adminConversationRepository.save(adminConversation);

        // Add to agent queue
        queueChatService.assignChatToAgent(
                modelMapper.map(conversation, ConversationDto.class),
                agent.getId()
        );

        // Ensure conversation is marked open
        conversation.setClosed(false);
        conversationRepository.save(conversation);
    }


    @Override
    @Transactional
    public ConversationDto consumeChatFromQueue(String agentId) {
        Employee employee = employeeRepository.findById(agentId)
                .orElseThrow(() -> new CustomException(404, "Agent not found"));

        ConversationDto conversation = queueChatService.dequeueConversationByTimestamp();
        if (conversation != null) {
            if (queueChatService.getAgentQueueSize(agentId) >= Integer.parseInt(settingRepository
                    .findByName("MaxAgentQueueSizeForMainQueue").getValue())) {
                throw new CustomException(400, "Agent queue is full!");
            }

            assignChatToAgent(conversation, agentId);

            Conversation conversationEntity = conversationRepository.findById(conversation.getId())
                    .orElseThrow(() -> new CustomException(404, "Conversation not found"));

            AdminConversation adminConversation = new AdminConversation();
            adminConversation.setEmployee(employee);
            adminConversation.setConversation(conversationEntity);
            adminConversation.setStartTime(LocalDateTime.now());
            adminConversationRepository.save(adminConversation);

            conversationRepository.save(conversationEntity);

            return conversation;
        } else {
            throw new CustomException(404, "No conversation found in the queue");
        }
    }

    @Override
    public void transferChatToAgent(String conversationId, String fromAgentId, String toAgentId) {
        Conversation conversation = conversationRepository.findById(conversationId)
                .orElseThrow(() -> new CustomException(404, "Conversation not found"));

        ConversationDto conversationDto = modelMapper.map(conversation, ConversationDto.class);
        Employee fromAgent = employeeRepository.findById(fromAgentId)
                .orElseThrow(() -> new CustomException(404, "From Agent not found!"));
        Employee toAgent = employeeRepository.findById(toAgentId)
                .orElseThrow(() -> new CustomException(404, "To Agent not found!"));

        if (toAgent.getAgentConversationStatus() == AgentConversationStatus.OFFLINE)
            throw new CustomException(400, "The agent is offline!");

        if (queueChatService.getAgentQueueSize(toAgentId) >= Integer.parseInt(settingRepository
                .findByName("MaxAgentQueueSizeForTransfer").getValue())) {
            throw new CustomException(400, "Agent queue is full!");
        }

        queueChatService.dequeueSpecificConversationFromAgentQueue(fromAgentId, conversationDto);
        assignChatToAgent(conversationDto, toAgentId);
    }

    @Override
    public void assignChatToAgent(String conversationId, String agentId) {
        employeeRepository.findById(agentId)
                .orElseThrow(() -> new CustomException(404, "Agent not found"));
        Conversation conversation = conversationRepository.findById(conversationId)
                .orElseThrow(() -> new CustomException(404, "Conversation not found"));

        conversation.setCreatedAt(LocalDateTime.now().minusMonths(1));
        conversationRepository.save(conversation);
        ConversationDto conversationDto = modelMapper.map(conversation, ConversationDto.class);
        queueChatService.assignChatToAgent(conversationDto, agentId);
    }

    @Override
    public void transferAgentQueueToMainQueue(String agentId) {
        employeeRepository.findById(agentId)
                .orElseThrow(() -> new CustomException(404, "Agent not found"));
        List<ConversationDto> extractedConversations = queueChatService.popAllConversationsFromAgentQueue(agentId);
        queueChatService.addAllToMainQueue(extractedConversations);
    }

    @Override
    public ConversationDto consumeChatFromAgentQueue(String agentId) {
        return queueChatService.dequeueConversationByTimestampFromAgentQueue(agentId);
    }

    @Override
    public List<ConversationDto> getAllAgentQueueContent(String agentId) {
        return queueChatService.getAgentQueue(agentId);
    }

    @Override
    public List<ConversationDto> getMainQueue() {
        return queueChatService.getMainQueue();
    }

    /**
     * Returns the position of a conversation in the queue.
     */
    @Override
    public int getConversationQueuePosition(String conversationId) {
        return queueChatService.getConversationPosition(conversationId);
    }


    @Transactional
    public void assignChatToAgent(ConversationDto conversation, String agentId) {
        employeeRepository.findById(agentId)
                .orElseThrow(() -> new CustomException(404, "Agent not found"));
        queueChatService.assignChatToAgent(conversation, agentId);
    }


    /// ///////////////////////////////////////////////////// Helpers ////////////////////////////////////////////////////////

    private void votePollOption(VoteRequestDto voteRequest, Conversation conversation, User user) {
        Poll poll = pollRepository.findById(voteRequest.getPollId())
                .orElseThrow(() -> new CustomException(404, "Poll note found!"));

        ChatMessage message = chatMessageRepository.findByConversationIdAndPollId(conversation.getId(), poll.getId())
                .orElseThrow(() -> new CustomException(400, "The poll is not belong to that conversation!"));

        if (poll.isClosed() || poll.getExpiryDateTime().isBefore(LocalDateTime.now()))
            throw new CustomException(400, "The poll has been closed!");

        Vote vote = createVote(voteRequest, poll, user);
        PollOption option = message.getPoll().getOptions()
                .stream()
                .filter(optionItem -> Objects.equals(optionItem.getId(), voteRequest.getOptionId()))
                .findFirst()
                .orElseThrow(() -> new CustomException(400, "The option is not belong to that poll!"));

        if (option.getVotes() == null)
            option.setVotes(new ArrayList<>());
        option.getVotes().add(vote);
        PollOption savedOption = pollOptionRepository.save(option);

        poll.getOptions().remove(option);
        poll.getOptions().removeIf(opt -> opt.getId().equals(savedOption.getId()));
        poll.getOptions().add(savedOption);
        Poll savedPoll = pollRepository.save(poll);

        message.setPoll(savedPoll);
        chatMessageRepository.save(message);
    }

    private void RetractVotePollOption(VoteRequestDto voteRequest, GroupConversation conversation, User user) {
        Poll poll = pollRepository.findById(voteRequest.getPollId())
                .orElseThrow(() -> new CustomException(404, "Poll note found!"));

        GroupChatMessage message = groupChatMessageRepository.findByConversationIdAndPollId(conversation.getId(), poll.getId())
                .orElseThrow(() -> new CustomException(400, "The poll is not belong to that conversation!"));

        if (poll.isClosed() || poll.getExpiryDateTime().isAfter(LocalDateTime.now()))
            throw new CustomException(400, "The poll has expired!");

        PollOption option = message.getPoll().getOptions()
                .stream()
                .filter(optionItem -> Objects.equals(optionItem.getId(), voteRequest.getOptionId()))
                .findFirst()
                .orElseThrow(() -> new CustomException(400, "The option is not belong to that poll!"));

        option.getVotes().removeIf(vote -> vote.getUser().getId().equals(user.getId()));

        PollOption savedOption = pollOptionRepository.save(option);

        poll.getOptions().remove(option);
        poll.getOptions().add(savedOption);
        Poll savedPoll = pollRepository.save(poll);

        message.setPoll(savedPoll);
        groupChatMessageRepository.save(message);
    }

    private void votePollOption(VoteRequestDto voteRequest, GroupConversation conversation, User user) {
        Poll poll = pollRepository.findById(voteRequest.getPollId())
                .orElseThrow(() -> new CustomException(404, "Poll note found!"));

        GroupChatMessage message = groupChatMessageRepository.findByConversationIdAndPollId(conversation.getId(), poll.getId())
                .orElseThrow(() -> new CustomException(400, "The poll is not belong to that conversation!"));

        if (poll.isClosed() || poll.getExpiryDateTime().isAfter(LocalDateTime.now()))
            throw new CustomException(400, "The poll has expired!");

        Vote vote = createVote(voteRequest, poll, user);
        PollOption option = message.getPoll().getOptions()
                .stream()
                .filter(optionItem -> Objects.equals(optionItem.getId(), voteRequest.getOptionId()))
                .findFirst()
                .orElseThrow(() -> new CustomException(400, "The option is not belong to that poll!"));

        option.getVotes().add(vote);
        PollOption savedOption = pollOptionRepository.save(option);

        poll.getOptions().remove(option);
        poll.getOptions().add(savedOption);
        Poll savedPoll = pollRepository.save(poll);

        message.setPoll(savedPoll);
        groupChatMessageRepository.save(message);
    }

    private Vote createVote(VoteRequestDto voteRequest, Poll poll, User user) {

        PollOption option = pollOptionRepository.findById(voteRequest.getOptionId())
                .orElseThrow(() -> new CustomException(404, "Option note found!"));

        poll.getOptions()
                .stream()
                .filter(item -> Objects.equals(item.getId(), option.getId()))
                .findFirst()
                .orElseThrow(() -> new CustomException(400, "This option is not belong to the presented poll!"));

        if (!poll.isMultiselect())
            poll.getOptions()
                    .forEach(optionItem -> {
                        if (optionItem.getVotes() != null) {
                            List<Vote> votesToRemove = option.getVotes()
                                    .stream()
                                    .filter(vote -> vote.getUser().getId().equals(user.getId()))
                                    .toList();

                            optionItem.getVotes().removeAll(votesToRemove);
                            votesToRemove.forEach(voteRepository::delete);
                        }
                    });

        Vote vote = new Vote();
        vote.setUser(modelMapper.map(user, SimpleUserinfoDto.class));

        return voteRepository.save(vote);
    }


    private ChatMessage createUpdateChatMessage(ChatMessage exitingMessage,
                                                ChatMessageRequestDto message,
                                                Conversation conversation) {
        // Case of new message
        if (exitingMessage == null) {
            ChatMessage newChatMessage = new ChatMessage();
            newChatMessage.setText(message.getText());
            newChatMessage.setType(message.getType());
            newChatMessage.setDateTime(LocalDateTime.now());
            newChatMessage.setStatus(message.getStatus());
            newChatMessage.setUrl(message.getUrl());
            newChatMessage.setReplyId(message.getReplyId());
            newChatMessage.setUser(conversation.getUser());
            newChatMessage.setConversation(conversation);
            newChatMessage.setPinned(message.isPinned());
            newChatMessage.setPinExpiryDate(message.getPinExpiryDate());
            newChatMessage.setReaction(message.getReaction());
            return newChatMessage;
        }

        // Case of update message
        else {
            exitingMessage.setText(message.getText());
            exitingMessage.setType(message.getType());
            exitingMessage.setDateTime(LocalDateTime.now());
            exitingMessage.setStatus(message.getStatus());
            exitingMessage.setUrl(message.getUrl());
            exitingMessage.setReplyId(message.getReplyId());
            exitingMessage.setUser(conversation.getUser());
            exitingMessage.setConversation(conversation);
            exitingMessage.setPinned(message.isPinned());
            exitingMessage.setPinExpiryDate(message.getPinExpiryDate());
            exitingMessage.setReaction(message.getReaction());
            return exitingMessage;
        }
    }

    private boolean isPollMessage(ChatMessageRequestDto message) {
        return message.getType() == ChatMessageType.Poll && message.getPoll() != null;
    }

    private void addPollToMessage(ChatMessage chatMessage, ChatMessageRequestDto message) {
        if (message.getPoll().getOptions() == null || message.getPoll().getOptions().isEmpty()) {
            throw new CustomException(400, "Poll must have at least one option");
        }

        Poll poll = createPoll(message.getPoll());
        chatMessage.setPoll(poll);
    }

    private GroupChatMessage createUpdateGroupChatMessage(GroupChatMessage existingMessage,
                                                          GroupChatMessageRequestDto messageRequest,
                                                          GroupConversation conversation,
                                                          UserConversation userConversation) {
        // Case of new message
        if (existingMessage == null) {
            GroupChatMessage newChatMessage = new GroupChatMessage();
            newChatMessage.setText(messageRequest.getText());
            newChatMessage.setType(messageRequest.getType());
            newChatMessage.setDateTime(LocalDateTime.now());
            newChatMessage.setStatus(messageRequest.getStatus());
            newChatMessage.setUrl(messageRequest.getUrl());
            newChatMessage.setUser(userConversation.getUser());
            newChatMessage.setReplyId(messageRequest.getReplyId());
            newChatMessage.setConversation(conversation);
            newChatMessage.setPinned(messageRequest.isPinned());
            newChatMessage.setPinExpiryDate(messageRequest.getPinExpiryDate());
            newChatMessage.setReaction(messageRequest.getReaction());
            return newChatMessage;
        }

        // Case of update message
        else {
            existingMessage.setText(messageRequest.getText());
            existingMessage.setType(messageRequest.getType());
            existingMessage.setDateTime(LocalDateTime.now());
            existingMessage.setStatus(messageRequest.getStatus());
            existingMessage.setUrl(messageRequest.getUrl());
            existingMessage.setUser(userConversation.getUser());
            existingMessage.setReplyId(messageRequest.getReplyId());
            existingMessage.setConversation(conversation);
            existingMessage.setPinned(messageRequest.isPinned());
            existingMessage.setPinExpiryDate(messageRequest.getPinExpiryDate());
            existingMessage.setReaction(messageRequest.getReaction());

            return existingMessage;
        }
    }

    private boolean isPollMessage(GroupChatMessageRequestDto message) {
        return message.getType() == ConversationMessageType.Poll && message.getPoll() != null;
    }

    private void addPollToGroupMessage(GroupChatMessage chatMessage, GroupChatMessageRequestDto message) {
        if (message.getPoll().getOptions() == null || message.getPoll().getOptions().isEmpty()) {
            throw new CustomException(400, "Poll must have at least one option");
        }

        Poll poll = createPoll(message.getPoll());
        chatMessage.setPoll(poll);
    }

    private Poll createPoll(PollRequestDto pollRequest) {
        Poll poll = new Poll();
        poll.setClosed(pollRequest.isClosed());
        poll.setExpiryDateTime(pollRequest.getExpiryDateTime());
        poll.setMultiselect(pollRequest.isMultiselect());
        poll.setQuestion(pollRequest.getQuestion());

        List<PollOption> pollOptions = pollRequest.getOptions().stream()
                .map(this::createPollOption)
                .toList();
        poll.setOptions(pollOptions);

        return pollRepository.save(poll);
    }

    private PollOption createPollOption(PollOptionRequestDto optionDto) {
        PollOption pollOption = new PollOption();
        pollOption.setAnswer(optionDto.getAnswer());
        return pollOptionRepository.save(pollOption);
    }

    private Poll updatePoll(Poll existingPoll, PollRequestDto pollRequest) {
        existingPoll.setClosed(pollRequest.isClosed());
        existingPoll.setQuestion(pollRequest.getQuestion());
        existingPoll.setMultiselect(existingPoll.isMultiselect());
        existingPoll.setExpiryDateTime(existingPoll.getExpiryDateTime());
        existingPoll.setOptions(updatePollOptions(existingPoll.getOptions(), pollRequest.getOptions()));
        return pollRepository.save(existingPoll);
    }

    private List<PollOption> updatePollOptions(List<PollOption> existingOptions, List<PollOptionRequestDto> pollOptionRequest) {
        Map<String, PollOptionRequestDto> pollOptionRequestMap = pollOptionRequest.stream()
                .collect(Collectors.toMap(
                        PollOptionRequestDto::getId,
                        item -> item
                ));
        existingOptions.forEach(pollOption -> {
            var existingPoll = pollOptionRequestMap.get(pollOption.getId());
            pollOption.setAnswer(existingPoll.getAnswer());
        });

        return pollOptionRepository.saveAll(existingOptions);
    }
}
