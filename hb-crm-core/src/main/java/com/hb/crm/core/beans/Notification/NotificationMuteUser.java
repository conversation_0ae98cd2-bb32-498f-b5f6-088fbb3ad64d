package com.hb.crm.core.beans.Notification;

import com.hb.crm.core.beans.User;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

@Getter
@Setter
@Document
@NoArgsConstructor
@AllArgsConstructor
public class NotificationMuteUser {
    @Id
    private String id;
    private String userId;
    private User mutedUser;
    private List<NotificationDisableSetting> muteDisableSetting;

    public NotificationMuteUser(String userId, User mutedUser) {
        this.userId = userId;
        this.mutedUser = mutedUser;
    }
}