package com.hb.crm.core.repositories;

import com.hb.crm.core.beans.HighLight;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.Optional;

public interface HighLightRepository extends MongoRepository<HighLight, String> {
    Optional<HighLight> findByNameAndInfluencer_Id(String name, String influencerId);
     // findbyid

    Optional<HighLight> findById(String id);
}
