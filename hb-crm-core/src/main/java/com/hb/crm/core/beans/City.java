package com.hb.crm.core.beans;

import com.hb.crm.core.beans.FlightPackage.Airport;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.DBRef;

import java.util.List;

@Setter
@Getter
@AllArgsConstructor
public class City {
    @Id
    private String id;
    private String name;
    private String description;
    @DBRef
    private Country country;
    private List<Airport> airportList;
    private Double latitude; // Latitude for map location
    private Double longitude; // Longitude for map location
    private List<MediaWrapper> medias;
    private String countryId;
    // map link url
    private String mapUrl;

    // constructor for the id in the country class


    public City(String id , String countryId)  {
        this.id = id;
        this.country = new Country(id);
    }
    public City() {
    }

}
