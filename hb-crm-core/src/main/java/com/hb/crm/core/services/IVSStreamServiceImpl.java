package com.hb.crm.core.services;

import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.ivs.AmazonIVS;
import com.amazonaws.services.ivs.AmazonIVSClientBuilder;
import com.amazonaws.services.ivs.model.*;
import com.hb.crm.core.Enums.NotificationEntityType;
import com.hb.crm.core.Enums.NotificationType;
import com.hb.crm.core.Enums.UserType;
import com.hb.crm.core.beans.User;
import com.hb.crm.core.dtos.LiveStream.LiveStreamStartResponseDto;
import com.hb.crm.core.services.interfaces.IVSStreamService;
import com.hb.crm.core.beans.LiveStream.LiveStream;
import com.hb.crm.core.repositories.LiveStream.LiveStreamRepository;
import com.hb.crm.core.Enums.Mp4ConversionStatus;
import com.hb.crm.core.services.interfaces.NotificationService;
import org.springframework.scheduling.annotation.Async;


import com.hb.crm.core.dtos.LiveStream.LiveStreamDownloadDto;
import lombok.RequiredArgsConstructor;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.amazonaws.HttpMethod;

import java.net.URL;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.*;
import java.nio.file.*;
import java.util.regex.Pattern;
import java.util.regex.Matcher;
import java.util.stream.Collectors;
import java.util.Map;
import java.util.Set;
import java.util.LinkedHashSet;
import java.util.HashMap;
import java.util.HashSet;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.ObjectMetadata;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.GeneratePresignedUrlRequest;
import com.amazonaws.services.s3.model.ListObjectsV2Request;
import com.amazonaws.services.s3.model.ListObjectsV2Result;
import com.amazonaws.services.s3.model.S3ObjectSummary;

@Service
@RequiredArgsConstructor
public class IVSStreamServiceImpl implements IVSStreamService {

        //private final LiveStreamConfigurationService configurationService;

        @Value("${cloud.aws.credentials.accessKey}")
        private String awsAccessKey;
        
        @Value("${cloud.aws.credentials.secretKey}")
        private String awsSecretKey;
        
        @Value("${cloud.aws.region.static}")
        private String awsRegion;
        @Value("${cloud.aws.ivs.recordingConfigurationArn}")
        private String recordingConfigurationArn;
        @Value("${cloud.aws.bucketName}")
        private String recordingsBucketName;
        @Value("${cloud.aws.ivs.ChannelType}")
        private String channelType;
        @Value("${cloud.aws.ivs.LatencyMode}")
        private String latencyMode;
        @Value("${cloud.aws.ivs.AwsAccountId}")
        private String awsAccountId ;
        private final S3Presigner s3Presigner;
        private final LiveStreamRepository liveStreamRepository;
        private final NotificationService notificationService;
        final static boolean test = false;
        @Override
        public LiveStreamStartResponseDto createChannelForInfluencer(String influencerId) 
        {
                if (test) 
                {
                        LiveStreamStartResponseDto mock = new LiveStreamStartResponseDto();
                        mock.setChannelArn("arn:aws:ivs:us-east-1:************:channel/mockChannel");
                        mock.setPlaybackUrl("https://mock.playback.aws.com/influencer_" + influencerId);
                        mock.setStreamKey("mock-stream-key-123");
                        mock.setIngestEndpoint("rtmps://mock.ingest.aws.com/app/");
                        return mock;
                }

                // Step 1: Create AWS credentials
                BasicAWSCredentials awsCreds = new BasicAWSCredentials(awsAccessKey, awsSecretKey);
                AmazonIVS ivs = AmazonIVSClientBuilder.standard()
                        .withCredentials(new AWSStaticCredentialsProvider(awsCreds))
                        .withRegion(awsRegion)
                        .build();

                // Step 2: Build CreateChannelRequest
                CreateChannelRequest channelRequest = new CreateChannelRequest()
                        .withName("influencer_" + influencerId+ "_" + System.currentTimeMillis())
                        .withType(channelType)
                        .withLatencyMode(latencyMode)
                        .withRecordingConfigurationArn(recordingConfigurationArn);
;
                // ListRecordingConfigurationsRequest request1 = new ListRecordingConfigurationsRequest();
                // ListRecordingConfigurationsResult result1 = ivs.listRecordingConfigurations(request1);

                // List<RecordingConfigurationSummary> configs1 = result1.getRecordingConfigurations();

                // for (RecordingConfigurationSummary config : configs1) {
                //         System.out.println("ARN: " + config.getArn());
                //         System.out.println("Name: " + config.getName());
                //         }
                // Step 3: Create the channel
                CreateChannelResult result = ivs.createChannel(channelRequest);
                Channel channel = result.getChannel();
                StreamKey streamKey = result.getStreamKey();

                // Step 4: Fetch full channel to get ingest endpoint
                Channel fullChannel = ivs.getChannel(new GetChannelRequest().withArn(channel.getArn())).getChannel();
                String ingestEndpoint = fullChannel.getIngestEndpoint(); // e.g., a1b2c3d4.global-contribute.live-video.net
                
                // Step 5: Build response DTO
                LiveStreamStartResponseDto response = new LiveStreamStartResponseDto();
                response.setChannelArn(channel.getArn());
                response.setPlaybackUrl(channel.getPlaybackUrl());
                response.setStreamKey(streamKey.getValue());
                response.setIngestEndpoint("rtmps://" + ingestEndpoint + "/app/");

                return response;
        }
        @Override
        public void stopStream(String channelArn) 
        {
                if (test) return;
                
                BasicAWSCredentials creds = new BasicAWSCredentials(awsAccessKey, awsSecretKey);
                AmazonIVS ivs = AmazonIVSClientBuilder.standard()
                        .withCredentials(new AWSStaticCredentialsProvider(creds))
                        .withRegion(awsRegion)
                        .build();

                try 
                {
                        // Try to stop stream first (ignore if it fails)
                        try 
                        {
                            ivs.stopStream(new StopStreamRequest().withChannelArn(channelArn));
                            Thread.sleep(2000); // Brief wait for stream to stop
                        } 
                        catch (Exception e) 
                        {
                            System.out.println("Stream stop failed or already offline: " + e.getMessage());
                        }
                        
                       
                        // Delete the channel
                        ivs.deleteChannel(new DeleteChannelRequest().withArn(channelArn));
                        System.out.println("Channel deleted successfully: " + channelArn);
                        
                } catch (Exception e) {
                        throw new RuntimeException("Failed to stop stream and delete channel: " + channelArn, e);
                }
        }
        

       
       
     
        /**
         * Extract channel ID from channel ARN
         */
        private String extractChannelIdFromArn(String channelArn) 
        {
                if (channelArn == null || !channelArn.contains("/")) 
                {
                        throw new IllegalArgumentException("Invalid channel ARN format: " + channelArn);
                }
                
                // ARN format: arn:aws:ivs:region:account:channel/channelId
                String[] parts = channelArn.split("/");
                return parts[parts.length - 1];
        }

        /**
         * Construct the S3 path where recordings are stored
         */
        private String constructRecordingPath(String channelId) 
        {
                // For recent recordings, you might want to search the last few days
                // This is more efficient than searching all recordings
                return "ivs/v1/" + awsAccountId + "/" + channelId + "/";
        }
    
        
          /**
         * Find HIGHEST QUALITY segments in CONTINUOUS sequence starting from 0
         * This fixes the quality mixing and repetition issues
         */
        private List<String> findAllSegmentsFromCompleteStream(AmazonS3 s3Client, List<S3ObjectSummary> objects) {
            System.out.println("🎬 ANALYZING STREAM FOR HIGHEST QUALITY CONTINUOUS SEQUENCE:");
            
            // Step 1: HighLight files by quality level and find TS segments only
            Map<String, List<S3ObjectSummary>> qualityGroups = new HashMap<>();
            
            for (S3ObjectSummary obj : objects) {
                String key = obj.getKey();
                
                // Only process TS segments
                if (!key.endsWith(".ts")) continue;
                
                // Extract quality from path (720p, 1080p, etc.)
                String quality = extractQualityFromPath(key);
                qualityGroups.computeIfAbsent(quality, k -> new ArrayList<>()).add(obj);
            }
            
            System.out.println("   📊 Found quality levels: " + qualityGroups.keySet());
            
            // Step 2: Find the highest quality level with the most segments
            String bestQuality = null;
            int maxSegments = 0;
            
            for (Map.Entry<String, List<S3ObjectSummary>> entry : qualityGroups.entrySet()) {
                String quality = entry.getKey();
                int segmentCount = entry.getValue().size();
                int qualityValue = parseQualityValue(quality);
                
                System.out.println("   🎥 Quality " + quality + ": " + segmentCount + " segments (" + qualityValue + "p)");
                
                // Prefer higher quality, but also consider segment count for completeness
                if 
                (
                    bestQuality == null || 
                    (qualityValue > parseQualityValue(bestQuality)) ||
                    (qualityValue == parseQualityValue(bestQuality) && segmentCount > maxSegments)) 
                {
                    bestQuality = quality;
                    maxSegments = segmentCount;
                }
            }
            
            if (bestQuality == null) 
            {
                return new ArrayList<>();
            }
                        
            // Step 3: Get all segments for the best quality and sort them by segment number
            List<S3ObjectSummary> bestQualitySegments = qualityGroups.get(bestQuality);
            
            // Sort segments by segment number (0.ts, 1.ts, 2.ts, etc.)
            bestQualitySegments.sort((s1, s2) -> 
            {
                int num1 = extractSegmentNumber(s1.getKey());
                int num2 = extractSegmentNumber(s2.getKey());
                return Integer.compare(num1, num2);
            });
            
            // Step 4: Find continuous sequence starting from 0
            List<String> continuousSegments = new ArrayList<>();
            int expectedSegmentNumber = 0;
            
            for (S3ObjectSummary segment : bestQualitySegments) 
            {
                int segmentNumber = extractSegmentNumber(segment.getKey());
                
                if (segmentNumber == expectedSegmentNumber) 
                {
                    continuousSegments.add(segment.getKey());
                    expectedSegmentNumber++;
                } 
                else if (segmentNumber > expectedSegmentNumber) 
                {
                    // Gap found - stop here to avoid incomplete sequences
                    break;
                }
            }
            
            System.out.println("   ✅ CONTINUOUS SEQUENCE FOUND:");
            System.out.println("      🎥 Quality: " + bestQuality);
            System.out.println("      📦 Segments: " + continuousSegments.size() + " (from 0 to " + (continuousSegments.size() - 1) + ")");
            System.out.println("      📅 First: " + extractSegmentIdentifier(continuousSegments.get(0)));
            System.out.println("      📅 Last: " + extractSegmentIdentifier(continuousSegments.get(continuousSegments.size() - 1)));
            
            // Verify no duplicates
            Set<String> uniqueSegments = new HashSet<>(continuousSegments);
            if (uniqueSegments.size() != continuousSegments.size()) 
            {
                System.out.println("   ⚠️  WARNING: Found " + (continuousSegments.size() - uniqueSegments.size()) + " duplicate segments!");
            }
            
            return continuousSegments;
        }
        
        /**
         * Extract quality level from S3 object path
         */
        private String extractQualityFromPath(String key) 
        {
            // AWS IVS path typically: ivs/v1/account/channel/year/month/day/hour/minute/session/quality/segment.ts
            String[] parts = key.split("/");
            
            // Look for quality indicators in the path
            for (String part : parts) 
            {
                if (part.matches("\\d+p\\d+") || part.matches("\\d+p")) 
                {
                    return part;
                }
            }
            
            // Look for quality in filename
            String filename = parts[parts.length - 1];
            if (filename.contains("720p")) return "720p";
            if (filename.contains("1080p")) return "1080p";
            if (filename.contains("480p")) return "480p";
            if (filename.contains("360p")) return "360p";
            
            return "unknown";
        }
        
        /**
         * Parse quality value as integer for comparison
         */
        private int parseQualityValue(String quality) 
        {
            if (quality.contains("1080")) return 1080;
            if (quality.contains("720")) return 720;
            if (quality.contains("480")) return 480;
            if (quality.contains("360")) return 360;
            if (quality.contains("240")) return 240;
            if (quality.contains("160")) return 160;
            return 0; // Unknown quality
        }
        
        /**
         * Extract segment number from TS filename
         */
        private int extractSegmentNumber(String key)
        {
            try {
                String filename = key.substring(key.lastIndexOf('/') + 1);
                
                // Remove .ts extension
                String nameWithoutExt = filename.replace(".ts", "");
                
                // Look for number patterns
                Pattern pattern = Pattern.compile("(\\d+)");
                Matcher matcher = pattern.matcher(nameWithoutExt);
                
                if (matcher.find()) {
                    return Integer.parseInt(matcher.group(1));
                }
            } catch (Exception e) {
                System.out.println("Failed to extract segment number from: " + key);
            }
            
            return -1; // Invalid segment number
        }
        
        /**
         * Convert HIGHEST QUALITY continuous sequence to MP4
         * No duplication, no quality mixing, proper segment ordering
         */
        private String performCompleteStreamToMp4Conversion(AmazonS3 s3Client, List<String> continuousSegmentKeys, String channelId) 
        {
            Path tempDir = null;
            try 
            {
                // Step 1: Create temporary directory for processing
                tempDir = Files.createTempDirectory("hq_stream_conversion_" + channelId);
                
                if (continuousSegmentKeys.isEmpty()) 
                {
                    return null;
                }               
                // Step 2: Download segments in exact sequence order
                List<Path> downloadedSegments = new ArrayList<>();
                
                for (int i = 0; i < continuousSegmentKeys.size(); i++) {
                    String segmentKey = continuousSegmentKeys.get(i);
                    int segmentNumber = extractSegmentNumber(segmentKey);
                
                    Path segmentFile = tempDir.resolve(String.format("seq_%04d.ts", i)); // Preserve sequence order
                    
                    try
                    {
                        s3Client.getObject(
                            new com.amazonaws.services.s3.model.GetObjectRequest(recordingsBucketName, segmentKey),
                            segmentFile.toFile()
                        );
                        downloadedSegments.add(segmentFile);
                        
                        
                    } 
                    catch (Exception e) 
                    {
                        System.out.println("Failed to download segment " + segmentNumber + ": " + segmentKey + " - " + e.getMessage());
                        // For continuous sequences, missing segments are critical
                        System.out.println("Missing segment in continuous sequence - conversion may be broken!");
                    }
                }
                
                if (downloadedSegments.isEmpty()) 
                {
                    return null;
                }
                
                // Step 3: Concatenate segments in exact order
                Path concatenatedFile = tempDir.resolve("high_quality_stream.ts");
                concatenateTsFiles(downloadedSegments, concatenatedFile);
                
                // Step 4: Convert to MP4 with high quality settings
                Path mp4File = tempDir.resolve("high_quality_stream.mp4");
                boolean conversionSuccess = convertTsToMp4WithHighQuality(concatenatedFile, mp4File);
                
                Path finalFile = conversionSuccess ? mp4File : concatenatedFile;
                String extension = conversionSuccess ? ".mp4" : ".ts";
                
                // Step 5: Upload to S3
                String outputFileName = "hq_stream_" + channelId + "_" + System.currentTimeMillis() + extension;
                String mp4Key = "converted-mp4/" + channelId + "/" + outputFileName;
                
                ObjectMetadata metadata = new ObjectMetadata();
                metadata.setContentLength(Files.size(finalFile));
                metadata.setContentType(conversionSuccess ? "video/mp4" : "video/mp2t");
                
                PutObjectRequest putRequest = new PutObjectRequest(
                    recordingsBucketName,
                    mp4Key,
                    finalFile.toFile()
                ).withMetadata(metadata);
                
                s3Client.putObject(putRequest);
                
                // Success summary
                System.out.println("✅ HIGH-QUALITY CONVERSION SUCCESSFUL:");
                System.out.println("   📁 Uploaded to: " + mp4Key);
                System.out.println("   📦 Segments processed: " + continuousSegmentKeys.size() + " (continuous from 0 to " + (continuousSegmentKeys.size() - 1) + ")");
                System.out.println("   💾 Successfully downloaded: " + downloadedSegments.size());
                System.out.println("   📱 Final file size: " + Files.size(finalFile) + " bytes");
                System.out.println("   🎥 Output format: " + (conversionSuccess ? "High-Quality MP4" : "TS"));
                
                
                if (downloadedSegments.size() == continuousSegmentKeys.size()) 
                {
                    System.out.println("PERFECT: Complete continuous sequence - no missing segments!");
                } 
                else
                {
                    int missing = continuousSegmentKeys.size() - downloadedSegments.size();
                    System.out.println("Missing segments: " + missing + " (sequence may be incomplete)");
                }
                
                return mp4Key;
                
            } 
            catch (Exception e) 
            {
                e.printStackTrace();
                return null;
            } 
            finally 
            {
                // Cleanup
                if (tempDir != null) 
                {
                    try 
                    {
                        Files.walk(tempDir)
                            .sorted((a, b) -> b.compareTo(a))
                            .forEach(path -> {
                                try 
                                {
                                    Files.deleteIfExists(path);
                                } 
                                catch (IOException e) {
                                    System.out.println("Failed to delete temp file: " + path);
                                }
                            });
                    } 
                    catch (Exception e) 
                    {
                        System.out.println("Failed to cleanup temp directory: " + e.getMessage());
                    }
                }
            }
        }
        
        /**
         * Convert TS to MP4 with HIGH QUALITY settings - no quality loss
         */
        private boolean convertTsToMp4WithHighQuality(Path inputTs, Path outputMp4) {
            try 
            {
                // Try different FFmpeg paths
                String[] ffmpegPaths = {
                    "C:\\ffmpeg\\bin\\ffmpeg.exe",  // Windows specific path
                    "C:\\ffmpeg\\ffmpeg.exe",       // Alternative Windows path
                    "ffmpeg",                       // System PATH
                    "/usr/bin/ffmpeg",              // Linux/Unix
                    "/usr/local/bin/ffmpeg"         // Alternative Linux/Unix
                };
                
                String ffmpegPath = null;
                
                // Find available FFmpeg
                for (String path : ffmpegPaths) {
                    try 
                    {
                        ProcessBuilder testPb = new ProcessBuilder(path, "-version");
                        testPb.redirectErrorStream(true);
                        Process testProcess = testPb.start();
                        int testExitCode = testProcess.waitFor();
                        
                        if (testExitCode == 0) 
                        {
                            ffmpegPath = path;
                            System.out.println("Found FFmpeg at: " + path);
                            break;
                        }
                    } 
                    catch (Exception e) 
                    {
                        // Try next path
                        continue;
                    }
                }
                
                if (ffmpegPath == null)
                {
                    System.out.println("FFmpeg not found - keeping as TS format");
                    return false;
                }
                
                // HIGH QUALITY conversion with minimal compression
                ProcessBuilder pb = new ProcessBuilder(
                    ffmpegPath,
                    "-i", inputTs.toString(),
                    "-c:v", "libx264",              // Video codec
                    "-preset", "slow",              // Slower preset = higher quality
                    "-crf", "18",                   // High quality (18 = visually lossless)
                    "-c:a", "aac",                  // Audio codec
                    "-b:a", "320k",                 // High quality audio bitrate
                    "-movflags", "faststart",       // Optimize for web playback
                    "-avoid_negative_ts", "make_zero", // Fix timestamp issues
                    "-y",                           // Overwrite output file
                    outputMp4.toString()
                );
                
                System.out.println("🎬 Converting with HIGH QUALITY settings (CRF 18, slow preset)...");
                
                // Capture output for debugging
                pb.redirectErrorStream(true);
                Process process = pb.start();
                
                // Read process output
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) 
                {
                    String line;
                    while ((line = reader.readLine()) != null) 
                    {
                        // Only log important FFmpeg messages
                        if (line.contains("Duration:") || line.contains("Stream") || line.contains("fps")) 
                        {
                            System.out.println("FFmpeg: " + line);
                        }
                    }
                }
                
                int exitCode = process.waitFor();
                
                if (exitCode == 0 && Files.exists(outputMp4) && Files.size(outputMp4) > 0) 
                {
                    System.out.println("✅ HIGH QUALITY MP4 conversion successful!");
                    System.out.println("   📱 Input TS size: " + Files.size(inputTs) + " bytes");
                    System.out.println("   📱 Output MP4 size: " + Files.size(outputMp4) + " bytes");
                    System.out.println("   🎥 Quality: CRF 18 (visually lossless)");
                    return true;
                } 
                else 
                {
                    System.out.println("❌ FFmpeg conversion failed with exit code: " + exitCode);
                    if (Files.exists(outputMp4)) {
                        System.out.println("   Output file size: " + Files.size(outputMp4) + " bytes");
                    }
                    return false;
                }
                
            }
            catch (Exception e) 
            {
                System.out.println("❌ High-quality conversion failed: " + e.getMessage());
                e.printStackTrace();
                return false;
            }
        }
        

        // Method to grant access to an S3 URL for a given object name
        public URL grantAccessUrl(String key) {
    
            // Generate the pre-signed URL with read access
            GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                    .bucket(recordingsBucketName)
                    .key(key)
                    .build();
    
            GetObjectPresignRequest presignRequest = GetObjectPresignRequest.builder()
                    .getObjectRequest(getObjectRequest)
                    .signatureDuration(Duration.ofHours(1)) // URL valid for 1 hour
                    .build();
    
            return s3Presigner.presignGetObject(presignRequest).url();
        }

        /**
         * Convert live stream recording from HLS (TS + M3U8) to MP4 and save to S3
         * @param channelArn The ARN of the channel containing the recording
         * @return Map containing conversion result and MP4 file key
         */
        public Map<String, Object> convertLiveStreamToMp4(String channelArn) {
            Map<String, Object> result = new HashMap<>();
            
            try {
                // Step 1: Extract channel ID from ARN
                String channelId = extractChannelIdFromArn(channelArn);
                
                // Step 2: Create S3 client
                BasicAWSCredentials awsCreds = new BasicAWSCredentials(awsAccessKey, awsSecretKey);
                AmazonS3 s3Client = AmazonS3ClientBuilder.standard()
                        .withCredentials(new AWSStaticCredentialsProvider(awsCreds))
                        .withRegion(awsRegion)
                        .build();

                // Step 3: Search for recordings in S3 bucket
                String recordingPrefix = constructRecordingPath(channelId);
                ListObjectsV2Request listRequest = new ListObjectsV2Request()
                        .withBucketName(recordingsBucketName)
                        .withPrefix(recordingPrefix)
                        .withMaxKeys(1000);

                ListObjectsV2Result listResult = s3Client.listObjectsV2(listRequest);
                
                if (listResult.getObjectSummaries().isEmpty()) {
                    result.put("status", "NO_RECORDINGS");
                    result.put("message", "No recordings found for this channel");
                    return result;
                }

                // Step 4: Check if MP4 already exists in converted directory
                String convertedPrefix = "converted-mp4/" + channelId + "/";
                ListObjectsV2Request convertedListRequest = new ListObjectsV2Request()
                        .withBucketName(recordingsBucketName)
                        .withPrefix(convertedPrefix)
                        .withMaxKeys(100);

                ListObjectsV2Result convertedListResult = s3Client.listObjectsV2(convertedListRequest);
                S3ObjectSummary existingMp4 = convertedListResult.getObjectSummaries().stream()
                        .filter(obj -> obj.getKey().endsWith(".mp4"))
                        .max(Comparator.comparing(S3ObjectSummary::getLastModified))
                        .orElse(null);

                if (existingMp4 != null) {
                    // MP4 already exists, return file key and download URL
                    URL downloadUrl = grantAccessUrl(existingMp4.getKey());
                    result.put("status", "COMPLETED");
                    result.put("mp4Key", existingMp4.getKey());
                    result.put("mp4DownloadUrl", downloadUrl.toString());
                    result.put("fileSize", existingMp4.getSize());
                    result.put("lastModified", existingMp4.getLastModified());
                    result.put("message", "MP4 file already exists");
                    return result;
                }

                // Step 5: Find ALL recording sessions for complete stream conversion
                List<String> allSegmentKeys = findAllSegmentsFromCompleteStream(s3Client, listResult.getObjectSummaries());
                
                if (allSegmentKeys.isEmpty()) {
                    result.put("status", "NO_SEGMENTS_FOUND");
                    result.put("message", "No video segments found for conversion");
                    return result;
                }

                // Step 6: Perform actual HLS to MP4 conversion with ALL segments
                String mp4Key = performCompleteStreamToMp4Conversion(s3Client, allSegmentKeys, channelId);
                
                if (mp4Key != null) {
                    // Conversion successful, return MP4 key and download URL
                    URL downloadUrl = grantAccessUrl(mp4Key);
                    result.put("status", "COMPLETED");
                    result.put("mp4Key", mp4Key);
                    result.put("mp4DownloadUrl", downloadUrl.toString());
                    result.put("channelId", channelId);
                    result.put("message", "Live stream successfully converted to MP4 and saved to S3");

                    return result;
                } else {
                    result.put("status", "CONVERSION_FAILED");
                    result.put("message", "Failed to convert HLS to MP4");
                    return result;
                }
                
            } catch (Exception e) {
                result.put("status", "ERROR");
                result.put("message", "Failed to convert live stream: " + e.getMessage());
                return result;
            }
        }

       
      
        /**
         * Extract a unique identifier from segment path for deduplication
         * This extracts the actual segment filename without the full path
         */
        private String extractSegmentIdentifier(String segmentKey) {
            if (segmentKey == null) return "";
            
            // Extract just the filename from the full path
            int lastSlash = segmentKey.lastIndexOf('/');
            String filename = lastSlash >= 0 ? segmentKey.substring(lastSlash + 1) : segmentKey;
            
            // Remove any query parameters or fragments
            int queryIndex = filename.indexOf('?');
            if (queryIndex >= 0) {
                filename = filename.substring(0, queryIndex);
            }
            
            return filename.toLowerCase(); // Normalize case for comparison
        }
        
      
        /**
         * Concatenate TS files (TS format allows direct concatenation)
         */
        private void concatenateTsFiles(List<Path> segments, Path outputFile) throws IOException {
            try (FileOutputStream output = new FileOutputStream(outputFile.toFile())) {
                for (Path segment : segments) {
                    try (FileInputStream input = new FileInputStream(segment.toFile())) {
                        byte[] buffer = new byte[8192];
                        int bytesRead;
                        while ((bytesRead = input.read(buffer)) != -1) {
                            output.write(buffer, 0, bytesRead);
                        }
                    }
                }
            }
            System.out.println("Concatenated " + segments.size() + " TS segments");
        }
        
         /**
          * Check if MP4 files exist for a channel and return download URL
          * @param channelId Channel ID for constructing output path
          * @return Map containing MP4 file info and download URL if available
          */
         public Map<String, Object> checkMp4Status(String channelId) {
             Map<String, Object> result = new HashMap<>();
             
             try {
                 BasicAWSCredentials awsCreds = new BasicAWSCredentials(awsAccessKey, awsSecretKey);
                 AmazonS3 s3Client = AmazonS3ClientBuilder.standard()
                         .withCredentials(new AWSStaticCredentialsProvider(awsCreds))
                         .withRegion(awsRegion)
                         .build();

                 // Look for MP4 files in the converted directory
                 String convertedPrefix = "converted-mp4/" + channelId + "/";
                 ListObjectsV2Request listRequest = new ListObjectsV2Request()
                         .withBucketName(recordingsBucketName)
                         .withPrefix(convertedPrefix)
                         .withMaxKeys(100);

                 ListObjectsV2Result listResult = s3Client.listObjectsV2(listRequest);
                 
                 // Find the most recent MP4 file
                 S3ObjectSummary latestMp4 = listResult.getObjectSummaries().stream()
                         .filter(obj -> obj.getKey().endsWith(".mp4"))
                         .max(Comparator.comparing(S3ObjectSummary::getLastModified))
                         .orElse(null);

                 if (latestMp4 != null) {
                     // MP4 exists! Generate download URL using grantAccessUrl
                     URL downloadUrl = grantAccessUrl(latestMp4.getKey());
                     
                     result.put("status", "AVAILABLE");
                     result.put("channelId", channelId);
                     result.put("mp4DownloadUrl", downloadUrl.toString());
                     result.put("mp4Key", latestMp4.getKey());
                     result.put("fileSize", latestMp4.getSize());
                     result.put("lastModified", latestMp4.getLastModified());
                     result.put("message", "MP4 file found and ready for download");
                     
                     return result;
                 }
                 
                 // No MP4 found
                 result.put("status", "NOT_FOUND");
                 result.put("channelId", channelId);
                 result.put("message", "No MP4 files found for this channel");
                 
                 return result;
                 
             } catch (Exception e) {
                 result.put("status", "ERROR");
                 result.put("channelId", channelId);
                 result.put("message", "Error checking MP4 status: " + e.getMessage());
                 return result;
             }
         }

        @Override
        @Async("mp4ConversionExecutor")
        public void convertToMp4Async(String streamId, String channelArn) {
            System.out.println("🎬 Starting background MP4 conversion for stream: " + streamId);
            
            try {
                // Find the LiveStream document
                LiveStream stream = liveStreamRepository.findById(streamId).orElse(null);
                if (stream == null) {
                    System.out.println("❌ Stream not found: " + streamId);
                    return;
                }
                
                // Update status to PROCESSING
                stream.setMp4ConversionStatus(Mp4ConversionStatus.PROCESSING);
                liveStreamRepository.save(stream);
                System.out.println("✅ Updated stream status to PROCESSING");
                
                // Perform the actual conversion using existing method
                Map<String, Object> result = convertLiveStreamToMp4(channelArn);
                
                // Update LiveStream document based on result
                if ("COMPLETED".equals(result.get("status"))) {
                    stream.setMp4ConversionStatus(Mp4ConversionStatus.COMPLETED);
                    stream.setMp4Key((String) result.get("mp4Key"));
                    
                    System.out.println("✅ Background conversion completed for stream: " + streamId);
                    System.out.println("📄 MP4 file saved as: " + stream.getMp4Key());

                    // Send notification to influencer
                    User user = stream.getInfulancer();
                    notificationService.sendAndStoreNotification(
                            stream.getId(),
                            NotificationType.LiveConversionCompleted,
                            user,
                            List.of(stream),
                            null,
                            null,
                            NotificationEntityType.LIVE_STREAM,
                            null,
                            null,
                            user.getUsername(),
                            UserType.Influencer);
                    
                } 
                else 
                {
                    stream.setMp4ConversionStatus(Mp4ConversionStatus.FAILED);
                    liveStreamRepository.save(stream);
                    User user = stream.getInfulancer();
                    notificationService.sendAndStoreNotification(
                            stream.getId(),
                            NotificationType.LiveConversionFailed,    // You'd need to add this enum
                            user,
                            List.of(stream),
                            null,
                            null,
                            NotificationEntityType.LIVE_STREAM,
                            null,
                            null,
                            user.getUsername(),
                            UserType.Influencer);
                    }  
            } 
            catch (Exception e) 
            {
                e.printStackTrace();
                // Update stream status to failed
                try
                {
                    LiveStream stream = liveStreamRepository.findById(streamId).orElse(null);
                    if (stream != null) 
                    {
                        stream.setMp4ConversionStatus(Mp4ConversionStatus.FAILED);
                        liveStreamRepository.save(stream);
                    }
                } 
                catch (Exception saveEx) 
                {
                    System.out.println("Failed to update stream status: " + saveEx.getMessage());
                }
            }
        }




}

