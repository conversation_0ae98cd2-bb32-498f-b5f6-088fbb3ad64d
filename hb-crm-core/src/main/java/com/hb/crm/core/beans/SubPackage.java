package com.hb.crm.core.beans;

import com.hb.crm.core.Enums.PackageStatus;
import com.hb.crm.core.Enums.PackageType;
import com.hb.crm.core.Enums.State;
import com.hb.crm.core.beans.Flight.Flight;
import com.hb.crm.core.beans.Hotel.Hotel;
import com.hb.crm.core.beans.PackagePlaces.PackageCountry;
import com.hb.crm.core.dtos.PackageEditRequest;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.DBRef;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@NoArgsConstructor
public class SubPackage {
    @Setter
    @Getter
    @Id
    private String id;

    @Setter
    @Getter
    private String name;

    @Setter
    @Getter
    private BigDecimal totalPrice;

    @Setter
    @Getter
    private PackageStatus packageStatus = PackageStatus.draft;

    @Setter
    @Getter
    private List<String> confirmFlightprices;

    @Setter
    @Getter
    private boolean privateDate = false;

    @Setter
    @Getter
    private int capacity;

    @Setter
    @Getter
    private LocalDateTime start;

    @Setter
    @Getter
    private LocalDateTime end;

    @Setter
    @Getter
    private Object details;

    @Setter
    @Getter
    private Object price;

    @Setter
    @Getter
    private String rejectionNote = "";

    @Setter
    @Getter
    private PackageType packageType = PackageType.TravelWithMe;

    @Setter
    @Getter
    private LocalDateTime creationDate;

    @Setter
    @Getter
    private LocalDateTime updateDate;

    @Setter
    @Getter
    private int followMeDiscount;

    @Setter
    @Getter
    private int numberOfRoom;

    @Setter
    @Getter
    private List<Flight> flights;

    @Setter
    @Getter
    private int subscribeCount;

    @Setter
    @Getter
    @DBRef(lazy = true)
    private Package _package;

    @Setter
    @Getter
    private List<PackageCountry> packagePlaces;

    @Setter
    @Getter
    private List<Hotel> hotels;

    @Setter
    @Getter
    @DBRef(lazy = true)
    private List<ActivityClones> activities;


    @Getter
    @Setter
    @Indexed
    private String slug;
    
    @Getter
    @Setter
    @Indexed
    private String followMeSlug;


    @Setter
    @Getter
    private List<Rate> rates;

    private int state = 0;

    @Setter
    @Getter
    private List<PackageEditRequest> modifyRequest;


    public State getState() {
        return State.fromValue(this.state);
    }

    public void setState(State state) {
        this.state = state.getValue();
    }

    public SubPackage(String id) {
        this.id = id;
    }


}