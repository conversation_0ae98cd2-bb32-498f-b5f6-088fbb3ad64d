package com.hb.crm.core.beans;

import org.springframework.data.annotation.Id;

import java.util.ArrayList;

public class Component {
    @Id
    private int id;
    private String code;
    private String type;
    private DataQuery dataQuery;
    private String order;
    private ArrayList<String> refIds;
    private String data;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public DataQuery getDataQuery() {
        return dataQuery;
    }

    public void setDataQuery(DataQuery dataQuery) {
        this.dataQuery = dataQuery;
    }

    public String getOrder() {
        return order;
    }

    public void setOrder(String order) {
        this.order = order;
    }

    public ArrayList<String> getRefIds() {
        return refIds;
    }

    public void setRefIds(ArrayList<String> refIds) {
        this.refIds = refIds;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }
}



