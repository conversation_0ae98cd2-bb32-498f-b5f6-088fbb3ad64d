package com.hb.crm.core.repositories;

import com.hb.crm.core.beans.FlightPackage.Airport;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface AirportRepository extends MongoRepository<Airport, String> {

    // Search by name, code, country, or city (case-insensitive)
    @Query("{ '$or': [ " +
            "{ 'name': { '$regex': ?0, '$options': 'i' } }, " +
            "{ 'code': { '$regex': ?0, '$options': 'i' } }, " +
            "{ 'country': { '$regex': ?0, '$options': 'i' } }, " +
            "{ 'city': { '$regex': ?0, '$options': 'i' } } ] }")
    List<Airport> findBySearchTerm(String searchTerm);

    // Find airports within 50km radius
    List<Airport> findByLatitudeBetweenAndLongitudeBetween(
            double minLat, double maxLat,
            double minLon, double maxLon
    );
    
    // Find airport by code (IATA/ICAO)
    Optional<Airport> findByCode(String code);
}
