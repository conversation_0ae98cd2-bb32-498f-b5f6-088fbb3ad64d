package com.hb.crm.core.beans.Notification;

import com.hb.crm.core.Enums.NotificationType;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Getter
@Setter
@Document
public class NotificationTemplateVariable {

    @Id
    private String id;
    private String name;
    private String label;
    private String entity;
    private NotificationType type;

}
