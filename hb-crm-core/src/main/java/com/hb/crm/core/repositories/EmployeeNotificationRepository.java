package com.hb.crm.core.repositories;

import com.hb.crm.core.beans.Notification.EmployeeNotification;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface EmployeeNotificationRepository extends MongoRepository<EmployeeNotification, String> {
    
    @Query("{'employee.id': ?0, 'sent': true, 'channelType': 'Push', 'lastTriedAt': {'$gte': ?1}, 'hidden': {'$ne': true}}")
    Page<EmployeeNotification> findByEmployeeIdAndLastTriedAtAfterAndHiddenFalseOrderBySentAtDesc(
            String employeeId, LocalDateTime lastTriedAt, Pageable pageable);
    
    @Query("{'employee.id': ?0, 'sent': true, 'read': false, 'channelType': 'Push', 'lastTriedAt': {'$gte': ?1}, 'hidden': {'$ne': true}}")
    Page<EmployeeNotification> findUnreadByEmployeeIdAndLastTriedAtAfterAndHiddenFalseOrderBySentAtDesc(
            String employeeId, LocalDateTime lastTriedAt, Pageable pageable);
    
    List<EmployeeNotification> findTop50BySentFalseOrderByCreatedAtAsc();
    
    Page<EmployeeNotification> findBySent(boolean sent, Pageable pageable);
    
    List<EmployeeNotification> findAllById(Iterable<String> ids);
    
    @Query(value = "{'employee.id': ?0, 'sent': true, 'read': ?1, 'hidden': ?2, 'createdAt': {'$gte': ?3}, 'channelType': 'Push'}", count = true)
    long customCountMethod(String employeeId, boolean read, boolean hidden, LocalDateTime createdAt);
}
