package com.hb.crm.core.beans.FlightPackage;

import com.hb.crm.core.beans.MediaWrapper;
import lombok.Data;

import java.util.List;

@Data
public class Airport {
    private String id;
    private String name;
    private String code; // IATA or ICAO code
    private String country;
    private String city;
    private double latitude;
    private double longitude;
    private String cityId;
    private String countryId;
    private String area;
    private String addressLink;
    private List<MediaWrapper> medias;
    private String description;
    private Double rate;
}
