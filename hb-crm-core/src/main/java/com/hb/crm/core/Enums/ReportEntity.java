package com.hb.crm.core.Enums;

import io.swagger.v3.oas.annotations.media.Schema;

public enum ReportEntity {

    @Schema(description = "A user-created post.")
    Post,

    @Schema(description = "A purchasable package.")
    Package,

    @Schema(description = "A comment made by a user.")
    Comment,

    @Schema(description = "A reply to a comment.")
    Reply,

    @Schema(description = "Media content such as images or videos.")
    Media,

    @Schema(description = "A user account.")
    User,

    @Schema(description = "A message in a chat.")
    ChatMessage,

    @Schema(description = "A message in a group chat.")
    GroupChatMessage,

    @Schema(description = "A one-on-one conversation.")
    Conversation,

    @Schema(description = "A group conversation.")
    GroupConversation,

    @Schema(description = "A live stream broadcast.")
    LiveStream
}

