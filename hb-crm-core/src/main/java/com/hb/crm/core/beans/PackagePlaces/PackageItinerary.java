package com.hb.crm.core.beans.PackagePlaces;

import com.hb.crm.core.Enums.State;
import com.hb.crm.core.beans.ActivityCategory;
import com.hb.crm.core.dtos.ActivityContentDto;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class PackageItinerary {

    private String name;
    private String details;
    private LocalDateTime start;
    private LocalDateTime end;
    private  String referenceId;
    private State state ;
    private ActivityContentDto activityContent;
    private ActivityCategory Category;
    private Object OtherDetail;
}
