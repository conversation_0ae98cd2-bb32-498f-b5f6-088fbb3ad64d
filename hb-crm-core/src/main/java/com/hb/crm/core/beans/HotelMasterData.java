package com.hb.crm.core.beans;

import com.hb.crm.core.beans.Hotel.Amenity;
import com.hb.crm.core.beans.Hotel.Image;
import com.hb.crm.core.beans.Hotel.Room;
import com.hb.crm.core.beans.Hotel.SocialMediaLink;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class HotelMasterData {
    @Id
    private String id;
    private ArrayList<Room> rooms;
    private String propertyId;
    private String propertyReferenceId;
    private String name;
    private float latitude;
    private float longitude;
    private List<String> addressLine;
    private String city;
    private String cityId;
    private String countryId;
    private String stateProvince;
    private String countryCode;
    private String description;
    private ArrayList<Amenity> amenities;
    private ArrayList<Image> images;
    private String amenityDescriptions;
    private BigDecimal nightlyAveragePrice;
    private BigDecimal price;
    private Double rate;
    private String phoneNumber;
    private List<SocialMediaLink> socialMediaLinks;
    private  String email;
    private  String addressLink;

}
