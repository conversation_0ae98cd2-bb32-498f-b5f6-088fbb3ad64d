package com.hb.crm.core.services.chat;

import com.hb.crm.core.beans.Employee;
import com.hb.crm.core.beans.chat.QueuedConversation;
import com.hb.crm.core.dtos.chat.response.ConversationDto;
import com.hb.crm.core.exceptions.CustomException;
import com.hb.crm.core.repositories.EmployeeRepository;
import com.hb.crm.core.repositories.chat.QueuedConversationRepository;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;

@Service
public class QueueChatService {

    private final QueuedConversationRepository queuedConversationRepository;
    private final EmployeeRepository employeeRepository;


    public QueueChatService(QueuedConversationRepository queuedConversationRepository, EmployeeRepository employeeRepository) {
        this.queuedConversationRepository = queuedConversationRepository;
        this.employeeRepository = employeeRepository;
    }
    

    public void enqueueConversationByTimestamp(ConversationDto conversation) {
        // Create and save a QueuedConversation entity to the database
        QueuedConversation queuedConversation = new QueuedConversation();
        queuedConversation.setTimestamp(conversation.getCreatedAt().toInstant(ZoneOffset.UTC).toEpochMilli());
        queuedConversation.setConversationData(conversation);
        queuedConversation.setAddedToQueueTimeStamp(LocalDateTime.now().toInstant(ZoneOffset.UTC).toEpochMilli());

        queuedConversationRepository.save(queuedConversation);
    }

    public ConversationDto dequeueConversationByTimestamp() {
        // Find the oldest conversation in the queue (sorted by timestamp)
        List<QueuedConversation> result = queuedConversationRepository.findByOrderByTimestampAsc();

        if (result != null && !result.isEmpty()) {
            QueuedConversation queuedConversation = result.getFirst();
            ConversationDto conversation = queuedConversation.getConversationData();

            // Remove the conversation from the queue
            queuedConversationRepository.delete(queuedConversation);

            return conversation;
        }
        return null;
    }

    public List<ConversationDto> getMainQueue() {
        // Retrieve all queued conversations ordered by timestamp
        List<QueuedConversation> queuedConversations = queuedConversationRepository.findByOrderByTimestampAsc();
        
        if (queuedConversations != null && !queuedConversations.isEmpty()) {
            return queuedConversations.stream()
                    .map(QueuedConversation::getConversationData)
                    .toList();
        }
        return Collections.emptyList();
    }


    public ConversationDto dequeueConversationByTimestampFromAgentQueue(String agentId) {
        // Find the employee
        Employee employee = employeeRepository.findById(agentId)
            .orElseThrow(() -> new CustomException(404, "Agent not found"));
        
        // Get the agent's queue
        List<QueuedConversation> agentQueue = employee.getAgentQueue();
        
        if (agentQueue != null && !agentQueue.isEmpty()) {
            // Sort by timestamp to get the oldest conversation
            agentQueue.sort(Comparator.comparing(QueuedConversation::getTimestamp));
            
            // Get the oldest conversation
            QueuedConversation queuedConversation = agentQueue.getFirst();
            ConversationDto conversation = queuedConversation.getConversationData();
            
            // Remove from the agent's queue
            agentQueue.removeFirst();
            employee.setAgentQueue(agentQueue);
            employeeRepository.save(employee);
            
            return conversation;
        }
        return null;
    }


    public void assignChatToAgent(ConversationDto conversation, String agentId) {
        // Find the employee
        Employee employee = employeeRepository.findById(agentId)
            .orElseThrow(() -> new CustomException(404, "Agent not found"));
        
        // Create a new QueuedConversation
        QueuedConversation queuedConversation = new QueuedConversation();
        queuedConversation.setTimestamp(conversation.getCreatedAt().toInstant(ZoneOffset.UTC).toEpochMilli());
        queuedConversation.setConversationData(conversation);
        queuedConversation.setAddedToQueueTimeStamp(LocalDateTime.now().toInstant(ZoneOffset.UTC).toEpochMilli());

        // Get the agent's queue or initialize if null
        List<QueuedConversation> agentQueue = employee.getAgentQueue();
        if (agentQueue == null) {
            agentQueue = new ArrayList<>();
        }
        
        // Add the conversation to the agent's queue
        agentQueue.add(queuedConversation);
        employee.setAgentQueue(agentQueue);
        
        // Save the employee with the updated queue
        employeeRepository.save(employee);
    }


    public List<ConversationDto> getAgentQueue(String agentId) {
        // Find the employee
        Employee employee = employeeRepository.findById(agentId)
            .orElseThrow(() -> new CustomException(404, "Agent not found"));
        
        // Get the agent's queue
        List<QueuedConversation> agentQueue = employee.getAgentQueue();
        
        if (agentQueue != null && !agentQueue.isEmpty()) {
            // Sort by timestamp (optional if you want to ensure order)
            agentQueue.sort(Comparator.comparing(QueuedConversation::getTimestamp));
            
            // Convert to a list of ConversationDto
            return agentQueue.stream()
                    .map(QueuedConversation::getConversationData)
                    .toList();
        }
        return Collections.emptyList();
    }

    // Check the size of agent queue
    public long getAgentQueueSize(String agentId) {
        Employee employee = employeeRepository.findById(agentId)
            .orElseThrow(() -> new CustomException(404, "Agent not found"));
        
        List<QueuedConversation> agentQueue = employee.getAgentQueue();
        return agentQueue != null ? agentQueue.size() : 0;
    }

    public void dequeueSpecificConversationFromAgentQueue(String agentId, ConversationDto targetConversation) {
        Employee employee = employeeRepository.findById(agentId)
            .orElseThrow(() -> new CustomException(404, "Agent not found"));
        
        List<QueuedConversation> agentQueue = employee.getAgentQueue();
        if (agentQueue == null || agentQueue.isEmpty()) {
            return;
        }
        
        agentQueue.removeIf(qc ->
            qc.getConversationData().getId().equals(targetConversation.getId()));
        
        employee.setAgentQueue(agentQueue);
        employeeRepository.save(employee);
    }

     // Pop all conversations from an agent's queue
    public List<ConversationDto> popAllConversationsFromAgentQueue(String agentId) {
        Employee employee = employeeRepository.findById(agentId)
            .orElseThrow(() -> new CustomException(404, "Agent not found"));
        
        List<QueuedConversation> agentQueue = employee.getAgentQueue();
        
        if (agentQueue != null && !agentQueue.isEmpty()) {
            // Get all conversations before clearing
            List<ConversationDto> conversations = agentQueue.stream()
                    .map(QueuedConversation::getConversationData)
                    .toList();
            
            // Clear the queue
            employee.setAgentQueue(new ArrayList<>());
            employeeRepository.save(employee);
            
            return conversations;
        }
        return Collections.emptyList();
    }

    // Add multiple conversations to the main queue
    public void addAllToMainQueue(List<ConversationDto> conversations) {
        for (ConversationDto conversation : conversations) {
            QueuedConversation queuedConversation = new QueuedConversation();
            queuedConversation.setTimestamp(conversation.getCreatedAt().toInstant(ZoneOffset.UTC).toEpochMilli());
            queuedConversation.setConversationData(conversation);
            queuedConversation.setAddedToQueueTimeStamp(LocalDateTime.now().toInstant(ZoneOffset.UTC).toEpochMilli());

            queuedConversationRepository.save(queuedConversation);
        }
    }

    /**
     * Returns the 1-based position of a conversation in the queue.
     * Returns -1 if not found.
     */
    public int getConversationPosition(String conversationId) {
        List<QueuedConversation> queue = queuedConversationRepository.findByOrderByTimestampAsc();

        for (int i = 0; i < queue.size(); i++) {
            if (queue.get(i).getConversationData().getId().equals(conversationId)) {
                return i + 1; // 1-based position
            }
        }

        return -1; // not found
    }
}
