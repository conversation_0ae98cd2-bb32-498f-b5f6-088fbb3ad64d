package com.hb.crm.core.repositories;

import com.hb.crm.core.beans.Notification.EmployeeNotificationMuteUser;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface EmployeeNotificationMuteUserRepository extends MongoRepository<EmployeeNotificationMuteUser, String> {
    Optional<EmployeeNotificationMuteUser> findByEmployeeIdAndMutedUser_Id(String employeeId, String mutedUserId);
}
