package com.hb.crm.core.beans;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.DBRef;

import java.time.LocalDateTime;
import java.util.List;

public class Reply {
    @Id
    private  String id;
    private  String comment;
    @DBRef
    private  Comment refcomment;
    @DBRef
    private  User user;
    @DBRef
    private  List<User> mentions;
    private  int numberOfReactions=0;

    private LocalDateTime createdDate;
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public List<User> getMentions() {
        return mentions;
    }

    public void setMentions(List<User> mentions) {
        this.mentions = mentions;
    }

    public Comment getRefcomment() {
        return refcomment;
    }

    public void setRefcomment(Comment refcomment) {
        this.refcomment = refcomment;
    }

    public int getNumberOfReactions() {
        return numberOfReactions;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public void setNumberOfReactions(int numberOfReactions) {
        this.numberOfReactions = numberOfReactions;
    }
}
