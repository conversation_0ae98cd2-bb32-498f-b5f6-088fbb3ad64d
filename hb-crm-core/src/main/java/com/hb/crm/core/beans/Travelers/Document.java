package com.hb.crm.core.beans.Travelers;

public class Document {
    private String documentType="PASSPORT";
     private String birthPlace;
    private String issuanceLocation;
    private String issuanceDate;
    private String number;
    private String expiryDate;
    private String issuanceCountry;
    private String validityCountry;
    private String nationality;

    private  boolean holder=true;
    private  String travelerType="Adult";

    public boolean isHolder() {
        return holder;
    }

    public void setHolder(boolean holder) {
        this.holder = holder;
    }

    public String getTravelerType() {
        return travelerType;
    }

    public void setTravelerType(String travelerType) {
        this.travelerType = travelerType;
    }

    public String getDocumentType() {
        return documentType;
    }

    public void setDocumentType(String documentType) {
        this.documentType = documentType;
    }

    public String getBirthPlace() {
        return birthPlace;
    }

    public void setBirthPlace(String birthPlace) {
        this.birthPlace = birthPlace;
    }

    public String getIssuanceLocation() {
        return issuanceLocation;
    }

    public void setIssuanceLocation(String issuanceLocation) {
        this.issuanceLocation = issuanceLocation;
    }

    public String getIssuanceDate() {
        return issuanceDate;
    }

    public void setIssuanceDate(String issuanceDate) {
        this.issuanceDate = issuanceDate;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(String expiryDate) {
        this.expiryDate = expiryDate;
    }

    public String getIssuanceCountry() {
        return issuanceCountry;
    }

    public void setIssuanceCountry(String issuanceCountry) {
        this.issuanceCountry = issuanceCountry;
    }

    public String getValidityCountry() {
        return validityCountry;
    }

    public void setValidityCountry(String validityCountry) {
        this.validityCountry = validityCountry;
    }

    public String getNationality() {
        return nationality;
    }

    public void setNationality(String nationality) {
        this.nationality = nationality;
    }


}
