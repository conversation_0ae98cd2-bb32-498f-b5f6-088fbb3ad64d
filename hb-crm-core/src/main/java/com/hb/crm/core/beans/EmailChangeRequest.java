package com.hb.crm.core.beans;

import lombok.Data;
import org.springframework.data.annotation.Id;

import java.time.Instant;

@Data
public class EmailChangeRequest {
    @Id
    private String id; // token ID (or UUID)
    private String userId;
    private String newEmail;
    private String oldEmail;
    private String token; // optionally hashed
    private Instant createdAt;
    private String status; // PENDING, COMPLETED
}
