package com.hb.crm.core.repositories;

import com.hb.crm.core.beans.Notification.Notification;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface NotificationRepository extends MongoRepository<Notification, String> {

    @Query("{'user.id': ?0, 'sent': true, 'channelType': 'Push', 'lastTriedAt': {'$gte': ?1}, 'hidden': {'$ne': true}}")
    Page<Notification> findByUserIdAndLastTriedAtAfterAndHiddenFalseOrderBySentAtDesc(String userId, LocalDateTime date, Pageable pageable);

    @Query("{'user.id': ?0, 'sent': true, 'read': false, 'channelType': 'Push', 'lastTriedAt': {'$gte': ?1}, 'hidden': {'$ne': true}}")
    Page<Notification> findUnreadByUserIdAndLastTriedAtAfterAndHiddenFalseOrderBySentAtDesc(String userId, LocalDateTime date, Pageable pageable);

    List<Notification> findTop50BySentFalseOrderByCreatedAtAsc();

    Page<com.hb.crm.core.beans.Notification.Notification> findBySent(Boolean sent, Pageable pageable);


    @Query(value = "{'user.id': ?0, 'sent': true, 'read': ?1, 'hidden': ?2, 'createdAt': {'$gte': ?3}, 'channelType': 'Push'}", count = true)
    long customCountMethod(String userId, boolean read, boolean hidden, LocalDateTime createdAt);
}
