package com.hb.crm.core.repositories;

import com.hb.crm.core.beans.Employee;
import com.hb.crm.core.beans.Notification.EmployeeNotificationSetting;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EmployeeNotificationSettingRepository extends MongoRepository<EmployeeNotificationSetting, String> {
    List<EmployeeNotificationSetting> findByEmployee(Employee employee);
    EmployeeNotificationSetting findByEmployee_Id(String employeeId);
}
