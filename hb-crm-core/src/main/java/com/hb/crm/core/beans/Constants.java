package com.hb.crm.core.beans;

public class Constants {

    public static final String SESSION_USER = "user";

    public static final String USER_ACCOUNT_EMAIL = "email";
    public static final String USER_ACCOUNT_ID = "contractNo";
    public static final String USER_ADNOC_REF_ID = "adnocRefID";
    public static final String USER_APARTMENT_NO = "appartmentNumber";
    public static final String USER_NAME = "name";
    public static final String USER_MOBILE = "mobile";
    public static final String USER_CITY = "city";
    public static final String USER_COMPANY = "company";
    public static final String USER_BUILDING_NO = "buildingNumber";
    public static final String USER_ENABLE = "enable";
    public static final String USER_STATUS = "status";
    public static final String USER_BUILDING_NAME = "buildingName";

    public static final String ID = "id";


    public static final String PASS_RESET_KEY = "passResetKey";
    public static final String ACTIVATION_KEY = "activationKey";
    public static final String BILL_CONTRACT_NUMBER = "contractNo";
    public static final String BILL_COMPANY = "company";
    public static final String ONLINE_FEES_NAME = "online.fees";
    public static final String BILL_INV_DATE = "billDate";
    public static final String ONLINE_MAX_PAY_NAME = "max.payment";
    public static final String ONLINE_MIN_PAY_NAME = "min.payment";

    public static final String TICKET_CONTRACT_NUMBER = "contractNo";
    public static final String TICKET_COMPANY = "company";
    public static final String TICKET_CITY = "city";
    public static final String TICKET_CAR_ID = "carId";
    public static final String TICKET_ASSINGEE_ID = "assignee";


    public static final String SYS_PAY_CONTRACT_NUMBER = "contractNo";
    public static final String SYS_PAY_COMPANY = "company";
    public static final String SYS_PAY_DOC_DATE = "docDate";


    public static final String ERRORS_CATEGORY_ACCESS = "ERROR_MSG_ACCESS";
    public static final String ERRORS_CATEGORY_REGISTER = "ERROR_MSG_REGISTER";
    public static final String ERRORS_CATEGORY_LOGIN = "ERROR_MSG_LOGIN";
    public static final String ERRORS_CATEGORY_CLIENT_EDITPROFILE = "ERROR_MSG_CLIENT_EDIT";
    public static final String ERRORS_CATEGORY_CLIENT_PASSWORD = "ERROR_MSG_CLIENT_CHANGEPASSWORD";
    public static final String ERRORS_CATEGORY_PASSWORD_RESET = "ERRORS_CATEGORY_PASSWORD_RESET";
    public static final String ERRORS_CATEGORY_BILL = "ERRORS_CATEGORY_BILL";
    public static final String ERRORS_CATEGORY_PAYMENT = "ERRORS_CATEGORY_PAYMENT";
    public static final String ERRORS_CATEGORY_CLIENT_METER_READING = "ERRORS_CATEGORY_CLIENT_METER_READING";

    public static final String USER_SESSION_NAME = "userSession";
    public static final String BILLS_SESSION_NAME = "bills";
    public static final String TXN_SESSION_NAME = "lastTxn";
    public static final String USER_PAGE_VIEW_NAME = "toView";

    public static final String ACCESS_CODE_AD = "payment.vpc_AccessCode.AD";
    public static final String ACCESS_CODE_DU = "payment.vpc_AccessCode.DU";
    public static final String MERCHANT_ID_AD = "payment.vpc_Merchant.AD";
    public static final String MERCHANT_ID_DU = "payment.vpc_Merchant.DU";
    public static final String SECURE_HASH_CODE_AD = "payment.SecureHashCode.AD";
    public static final String SECURE_HASH_CODE_DU = "payment.SecureHashCode.DU";
    public static final String RETURN_URL = "payment.vpc_ReturnURL";
    public static final String REG_RETURN_URL = "payment.vpc_reg_ReturnURL";
    public static final String QUERY_USER_NAME = "payment.vpc_user_name";
    public static final String QUERY_USER_PASS = "payment.vpc_user_pass";
    public static final String MERCHANT_QUERY_URL = "payment.queryUrl";

    public static final String AVENUE_WORKING_KEY_AUH = "avenue.workingkey.auh";
    public static final String AVENUE_ACCESS_CODE_AUH = "avenue.accessCode.auh";
    public static final String AVE_MERCHANT_ID_AUH ="avenue.merchant.id.auh";
    public static final String AVE_REDIRECT_URL_OK_AUH = "avenue.redirectUrl.ok.auh";

    public static final String AVENUE_WORKING_KEY_ADN = "avenue.workingkey.adn";
    public static final String AVENUE_ACCESS_CODE_ADN = "avenue.accessCode.adn";
    public static final String AVE_MERCHANT_ID_ADN ="avenue.merchant.id.adn";
    public static final String AVE_REDIRECT_URL_OK_ADN = "avenue.redirectUrl.ok.adn";

    public static final String AVENUE_WORKING_KEY_DU = "avenue.workingkey.du";
    public static final String AVENUE_ACCESS_CODE_DU = "avenue.accessCode.du";
    public static final String AVE_MERCHANT_ID_DU ="avenue.merchant.id.du";
    public static final String AVE_REDIRECT_URL_OK_DU = "avenue.redirectUrl.ok.du";


    public static final String[] adnocBuildings = {"441","510","B4/B-1","B4/B-2","B5/B-1","B5/B-2","B5/B-3","B6/B-1","B6/B-2","B6/B-3","B13/B-1"};

    public static final String toAccountView = "/views/userPage.jsp";
    public static final String toAvenuePayDoPost = "/views/avenuePay/ccavRequestHandler.jsp";
    public static final String toLogin = "/views/login.jsp";


    public static final String DUBAI = "DUBAI";
    public static final String ABUDHABI = "ABUDHABI";

    public static final String TXN_FIELD_MERCHTXN_REF = "merchTxnRef";
    public static final String TXN_FIELD_ccbAccountId = "ccbAccountId";
    public static final String CCID = "ccid";
}
