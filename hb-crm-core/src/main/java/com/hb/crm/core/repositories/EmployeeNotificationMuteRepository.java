package com.hb.crm.core.repositories;

import com.hb.crm.core.Enums.NotificationEntityType;
import com.hb.crm.core.beans.Notification.EmployeeNotificationMute;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface EmployeeNotificationMuteRepository extends MongoRepository<EmployeeNotificationMute, String> {
    Optional<EmployeeNotificationMute> findByEmployeeIdAndEntityIdAndEntityType(
            String employeeId, String entityId, NotificationEntityType entityType);
}
