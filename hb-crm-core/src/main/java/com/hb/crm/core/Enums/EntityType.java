package com.hb.crm.core.Enums;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(example = "React", description = "Type of Entity reaction (e.g., React, PostMark, Favourite, Subscribe, Follow, View,Share,SaveReel)")
public enum EntityType {
    @Schema(description = "React")
    React,
    @Schema(description = "PostMark")
    PostMark,
    @Schema(description = "Favourite")
    Favourite,
    @Schema(description = "Subscribe")
    Subscribe,
    @Schema(description = "Follow")
    Follow,
    @Schema(description = "View")
    View,
    @Schema(description = "Share")
    Share,
    @Schema(description = "SaveReel")
    SaveReel
}
