package com.hb.crm.core.beans.Notification;

import com.hb.crm.core.Enums.DisableNotificationType;
import com.hb.crm.core.Enums.NotificationChannelType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class NotificationDisableSetting {
    private DisableNotificationType disableNotificationType;
    private List<NotificationChannelType> channelTypes;
}
