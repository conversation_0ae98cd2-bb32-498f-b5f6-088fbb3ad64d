package com.hb.crm.core.beans.Travelers;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.ArrayList;
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class Traveler{
    private  String id;

    private String dateOfBirth;
    private Name name;
    private String gender="Male";
    private Contact contact;
    private String passportNumber;
    private ArrayList<Document> documents;

}
