package com.hb.crm.core.beans;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Mood {
    @Id
    private String id;
    private String type;
    private String Title;
    private MediaWrapper media;
    private MediaWrapper selectedMedia;
    private MediaWrapper mediaIcon;

    public Mood(String id) {
        this.id = id;
    }

}
