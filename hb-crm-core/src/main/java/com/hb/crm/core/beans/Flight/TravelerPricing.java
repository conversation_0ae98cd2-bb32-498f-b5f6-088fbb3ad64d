package com.hb.crm.core.beans.Flight;

import java.util.ArrayList;

public class TravelerPricing {
    private String travelerId;
    private String fareOption;
    private String travelerType;
    private String associatedAdultId;
    private Price price;
    private ArrayList<FareDetailsBySegment> fareDetailsBySegment;

    public String getTravelerId() {
        return travelerId;
    }

    public void setTravelerId(String travelerId) {
        this.travelerId = travelerId;
    }

    public String getFareOption() {
        return fareOption;
    }

    public void setFareOption(String fareOption) {
        this.fareOption = fareOption;
    }

    public String getTravelerType() {
        return travelerType;
    }

    public void setTravelerType(String travelerType) {
        this.travelerType = travelerType;
    }

    public String associatedAdultId() {
        return associatedAdultId;
    }

    public void setPrice(Price price) {
        this.price = price;
    }
    public Price getPrice() {
        return price;
    }
    public void setAssociatedAdultId(String associatedAdultId) {
        this.associatedAdultId = associatedAdultId;
    }

    public ArrayList<FareDetailsBySegment> getFareDetailsBySegment() {
        return fareDetailsBySegment;
    }

    public void setFareDetailsBySegment(ArrayList<FareDetailsBySegment> fareDetailsBySegment) {
        this.fareDetailsBySegment = fareDetailsBySegment;
    }
}
