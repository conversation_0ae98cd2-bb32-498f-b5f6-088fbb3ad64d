package com.hb.crm.core.beans.Flight;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.hb.crm.core.Enums.FlightStep;
import org.springframework.data.annotation.Id;

import java.math.BigDecimal;
import java.util.ArrayList;
@JsonIgnoreProperties(ignoreUnknown = true)

public class Flight {
    @Id
    private String searchIdentifier;
    private String type="flight-offer";
    private String id;
    private boolean instantTicketingRequired;
    private boolean nonHomogeneous;
    private boolean oneWay;
    private boolean paymentCardRequired;
    private String lastTicketingDate;
    private String flightReferenceId;
    private ArrayList<Itinerary> itineraries;
    private Price price;
    private Price businesPrice;
    private Price firstClassPrice;
    private  boolean isInternational;
    private  ArrayList<String> upgradeCabins;
    private PricingOptions pricingOptions;
    private ArrayList<String> validatingAirlineCodes;
    private ArrayList<TravelerPricing> travelerPricings;
    private FareRules fareRules;
    private ArrayList<Object> travelDocuments;
    private boolean brandedFare;
    private boolean isRoundTrip;
    private ArrayList<Aircraft> aircrafts;
    private ArrayList<Carrier> carriers;
    private ArrayList<Currency> currencies;
    private  ArrayList<Location> Locations;
    private  String origin;
    private  String destination;
    private  int stop;
    private  String upgradeCabin;

    private  String path;
    private  BigDecimal economyPrice;
    private  BigDecimal childEconomyPrice;
    private  BigDecimal childBusinessPrice;
    private  BigDecimal businessPrice;

    private FlightStep flightStep;


    public FlightStep getFlightStep() {
        return flightStep;
    }

    public void setFlightStep(FlightStep flightStep) {
        this.flightStep = flightStep;
    }

    public boolean isInternational() {
        return isInternational;
    }

    public void setInternational(boolean international) {
        isInternational = international;
    }

    public BigDecimal getChildEconomyPrice() {
        return childEconomyPrice;
    }

    public void setChildEconomyPrice(BigDecimal childEconomyPrice) {
        this.childEconomyPrice = childEconomyPrice;
    }

    public BigDecimal getChildBusinessPrice() {
        return childBusinessPrice;
    }

    public void setChildBusinessPrice(BigDecimal childBusinessPrice) {
        this.childBusinessPrice = childBusinessPrice;
    }

    public String getSearchIdentifier() {
        return searchIdentifier;
    }

    public BigDecimal getEconomyPrice() {
        return economyPrice;
    }

    public void setEconomyPrice(BigDecimal economyPrice) {
        this.economyPrice = economyPrice;
    }

    public BigDecimal getBusinessPrice() {
        return businessPrice;
    }

    public void setBusinessPrice(BigDecimal businessPrice) {
        this.businessPrice = businessPrice;
    }

    public void setSearchIdentifier(String searchIdentifier) {
        this.searchIdentifier = searchIdentifier;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public boolean isInstantTicketingRequired() {
        return instantTicketingRequired;
    }

    public void setInstantTicketingRequired(boolean instantTicketingRequired) {
        this.instantTicketingRequired = instantTicketingRequired;
    }

    public boolean isNonHomogeneous() {
        return nonHomogeneous;
    }

    public void setNonHomogeneous(boolean nonHomogeneous) {
        this.nonHomogeneous = nonHomogeneous;
    }

    public boolean isOneWay() {
        return oneWay;
    }

    public void setOneWay(boolean oneWay) {
        this.oneWay = oneWay;
    }

    public boolean isPaymentCardRequired() {
        return paymentCardRequired;
    }

    public void setPaymentCardRequired(boolean paymentCardRequired) {
        this.paymentCardRequired = paymentCardRequired;
    }

    public String getLastTicketingDate() {
        return lastTicketingDate;
    }

    public void setLastTicketingDate(String lastTicketingDate) {
        this.lastTicketingDate = lastTicketingDate;
    }

    public String getFlightReferenceId() {
        return flightReferenceId;
    }

    public void setFlightReferenceId(String flightReferenceId) {
        this.flightReferenceId = flightReferenceId;
    }

    public ArrayList<Itinerary> getItineraries() {
        return itineraries;
    }

    public void setItineraries(ArrayList<Itinerary> itineraries) {
        this.itineraries = itineraries;
    }

    public Price getPrice() {
        return price;
    }

    public void setPrice(Price price) {
        this.price = price;
    }

    public PricingOptions getPricingOptions() {
        return pricingOptions;
    }

    public void setPricingOptions(PricingOptions pricingOptions) {
        this.pricingOptions = pricingOptions;
    }

    public ArrayList<String> getValidatingAirlineCodes() {
        return validatingAirlineCodes;
    }

    public void setValidatingAirlineCodes(ArrayList<String> validatingAirlineCodes) {
        this.validatingAirlineCodes = validatingAirlineCodes;
    }

    public ArrayList<TravelerPricing> getTravelerPricings() {
        return travelerPricings;
    }

    public void setTravelerPricings(ArrayList<TravelerPricing> travelerPricings) {
        this.travelerPricings = travelerPricings;
    }

    public FareRules getFareRules() {
        return fareRules;
    }

    public void setFareRules(FareRules fareRules) {
        this.fareRules = fareRules;
    }

    public ArrayList<Object> getTravelDocuments() {
        return travelDocuments;
    }

    public void setTravelDocuments(ArrayList<Object> travelDocuments) {
        this.travelDocuments = travelDocuments;
    }

    public boolean isBrandedFare() {
        return brandedFare;
    }

    public void setBrandedFare(boolean brandedFare) {
        this.brandedFare = brandedFare;
    }

    public boolean isRoundTrip() {
        return isRoundTrip;
    }

    public void setRoundTrip(boolean roundTrip) {
        isRoundTrip = roundTrip;
    }

    public ArrayList<Aircraft> getAircrafts() {
        return aircrafts;
    }

    public void setAircrafts(ArrayList<Aircraft> aircrafts) {
        this.aircrafts = aircrafts;
    }

    public ArrayList<Carrier> getCarriers() {
        return carriers;
    }

    public void setCarriers(ArrayList<Carrier> carriers) {
        this.carriers = carriers;
    }

    public ArrayList<Currency> getCurrencies() {
        return currencies;
    }

    public void setCurrencies(ArrayList<Currency> currencies) {
        this.currencies = currencies;
    }

    public String getOrigin() {
        return origin;
    }

    public void setOrigin(String origin) {
        this.origin = origin;
    }



    public String getUpgradeCabin() {
        return upgradeCabin;
    }

    public void setUpgradeCabin(String upgradeCabin) {
        this.upgradeCabin = upgradeCabin;
    }

    public ArrayList<Location> getLocations() {
        return Locations;
    }

    public void setLocations(ArrayList<Location> locations) {
        Locations = locations;
    }

    public String getDestination() {
        return destination;
    }

    public void setDestination(String destination) {
        this.destination = destination;
    }

    public int getStop() {
        return stop;
    }

    public void setStop(int stop) {
        this.stop = stop;
    }

    public Price getBusinesPrice() {
        return businesPrice;
    }

    public void setBusinesPrice(Price businesPrice) {
        this.businesPrice = businesPrice;
    }

    public Price getFirstClassPrice() {
        return firstClassPrice;
    }

    public void setFirstClassPrice(Price firstClassPrice) {
        this.firstClassPrice = firstClassPrice;
    }

    public ArrayList<String> getUpgradeCabins() {
        return upgradeCabins;
    }

    public void setUpgradeCabins(ArrayList<String> upgradeCabins) {
        this.upgradeCabins = upgradeCabins;
    }
}




