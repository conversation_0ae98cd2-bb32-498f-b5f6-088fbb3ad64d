package com.hb.crm.core.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisClusterConfiguration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.util.List;

@Configuration
public class RedisConfig {

    @Value("${spring.redis.host}") // Inject the Redis host from application.properties
    private String redisHost;

    @Value("${spring.redis.port}") // Inject the Redis port from application.properties
    private int redisPort;

    @Value("${redis.active}")
    private Boolean redIsActive;

    @Bean
    @ConditionalOnProperty(name = "redis.active", havingValue = "true", matchIfMissing = false)
    public RedisConnectionFactory redisConnectionFactory() {
        RedisClusterConfiguration clusterConfig = new RedisClusterConfiguration(
                List.of(redisHost + ":" + redisPort)
        );
        return new LettuceConnectionFactory(clusterConfig);
    }

    @Bean
    @ConditionalOnProperty(name = "redis.active", havingValue = "true", matchIfMissing = false)
    public RedisTemplate<String, Object> redisTemplate() {
        RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(redisConnectionFactory());
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(new Jackson2JsonRedisSerializer<>(Object.class)); // Configure your serializer
        return redisTemplate;
    }

    // Your CacheManager bean remains the same

}
